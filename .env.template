# GENISYS Environment Configuration Template
# Copy this file to .env and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_NAME=genisys
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_PORT=5432

# Data Configuration
DATA_DIR=data
TICK_DATA_FILE=EURUSD_Ticks.csv
MASTER_DATA_FILE=GENISYS_EURUSD_MASTER.parquet

# Experiment Configuration
DEFAULT_CONFIG=config.json
DRY_RUN=false
DRY_RUN_BARS=100000

# Logging Configuration
LOG_LEVEL=INFO
LOG_DIR=logs

# Model Configuration
MODEL_DIR=models
RANDOM_STATE=42

# Performance Configuration
N_JOBS=-1
MEMORY_LIMIT_GB=8

# Development Settings
DEBUG=false
PROFILE_MEMORY=false
