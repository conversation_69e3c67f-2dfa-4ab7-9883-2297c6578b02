# GENISYS Distributed System Deployment Guide

Complete guide for deploying the distributed GENISYS trading research platform across ORION (64GB server) and APOLLO (128GB workstation).

## 🏗️ System Overview

```
ORION (64GB, Debian 12)          APOLLO (128GB, Debian 13)
├── PostgreSQL (Docker)          ├── GENISYS-ANALYTIC Worker
├── GENISYS-WEB (FastAPI)        │   ├── Resource: 88GB RAM, 32 workers
├── GENISYS-ANALYTIC Worker      │   ├── Full performance mode
│   ├── Resource: 45GB RAM       │   └── Model training & validation
│   ├── 16 workers               └── Shared model storage
│   └── Streaming mode           
└── Centralized database         
```

## 🚀 Quick Deployment

### Automated Setup

```bash
# On ORION (will setup web server + worker)
python deploy_genisys_system.py --component all --orion-ip <ORION_IP>

# On APOLLO (will setup worker only)
python deploy_genisys_system.py --component worker --orion-ip <ORION_IP>
```

### Manual Step-by-Step Setup

## 📋 Step 1: Setup ORION (Web Server + Database)

### 1.1 Database Setup

Your PostgreSQL is already running in Docker. Create the GENISYS database:

```bash
# Connect to your PostgreSQL container
docker exec -it <postgres_container_name> psql -U postgres

# Create database and user
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;
\q
```

Or use the generated SQL script:
```bash
python deploy_genisys_system.py  # This creates setup_database.sql
docker exec -i <postgres_container_name> psql -U postgres < setup_database.sql
```

### 1.2 GENISYS-WEB Server Setup

```bash
# Generate the web server project
python create_genisys_web.py

# Setup the web server
cd ../GENISYS-WEB
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL connection details

# Start the web server
python -m app.main
```

The web interface will be available at: `http://orion:8000`

### 1.3 ORION Worker Setup

```bash
# Back to GENISYS-ANALYTIC directory
cd ../GENISYS

# Install worker dependencies
pip install httpx asyncio-mqtt

# Configure for 64GB system
python manage_resources.py profile mid_range_64gb

# Create resource-aware configs
python manage_resources.py create-configs

# Start the worker
python worker_daemon.py --server-url http://localhost:8000 --worker-name ORION
```

## 📋 Step 2: Setup APOLLO (Worker Only)

### 2.1 Copy GENISYS-ANALYTIC Project

```bash
# Copy the entire GENISYS project to APOLLO
scp -r GENISYS/ apollo:/path/to/GENISYS/

# Or use git if you've committed the changes
git clone <repository> && cd GENISYS
```

### 2.2 Setup APOLLO Worker

```bash
# On APOLLO machine
cd GENISYS

# Setup virtual environment (if not already done)
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

# Install worker dependencies
pip install httpx asyncio-mqtt

# System will auto-detect as high-end (128GB)
python manage_resources.py info

# Create resource-aware configs
python manage_resources.py create-configs

# Start the worker (replace <ORION_IP> with actual IP)
python worker_daemon.py --server-url http://<ORION_IP>:8000 --worker-name APOLLO
```

## 🔧 Configuration Details

### Resource Configurations

**ORION (64GB) - Automatically Applied:**
- Max RAM Usage: 45GB (70% of 64GB)
- Workers: 16 (conservative for server stability)
- Chunk Size: 500,000 rows
- Streaming: Enabled
- GC Frequency: Every 10 operations

**APOLLO (128GB) - Automatically Detected:**
- Max RAM Usage: 88GB (70% of 128GB)
- Workers: 32 (80% of 40 logical cores)
- Chunk Size: 1,000,000 rows
- Streaming: Disabled
- GC Frequency: Every 20 operations

### Database Configuration

Update your `.env` file in GENISYS-WEB:

```env
# Database Configuration
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# If PostgreSQL is in Docker with custom port:
# DATABASE_URL=postgresql://genisys:genisys_password@localhost:5433/genisys_web
```

## 🌐 Web Interface Features

Once deployed, access `http://orion:8000` for:

### Strategy Builder
- Visual timeframe selection (S5, S10, M1, M2, M5, H1, D)
- Indicator configuration (EMA, RSI, ADX, MACD, Bollinger Bands)
- Parameter range definition for sweeps
- Resource requirement estimation

### Experiment Management
- Submit single experiments or parameter sweeps
- Monitor real-time progress across workers
- View worker status and resource usage
- Queue management and prioritization

### Results Analysis
- Performance ranking by Sharpe ratio, profit factor
- Interactive equity curves and drawdown charts
- Parameter correlation analysis
- Statistical significance testing

### Model Repository
- Browse and download trained models
- Model performance tracking
- Production deployment marking
- Version control and rollback

## 🔄 Operational Workflows

### Running a Single Experiment

1. **Access Web Interface**: `http://orion:8000`
2. **Strategy Builder**: Configure timeframes, indicators, parameters
3. **Submit Experiment**: System automatically assigns to best available worker
4. **Monitor Progress**: Real-time updates on execution
5. **View Results**: Performance metrics, equity curves, model files

### Running Parameter Sweeps

1. **Define Parameter Ranges**: Set min/max values and step sizes
2. **Resource Estimation**: System calculates total compute time
3. **Batch Submission**: Creates multiple experiments automatically
4. **Load Balancing**: Distributes across ORION and APOLLO workers
5. **Results Comparison**: Analyze performance across parameter space

### Model Management

1. **Automatic Saving**: Models saved during experiment execution
2. **Performance Tracking**: Validation scores and metrics stored
3. **Production Marking**: Flag best models for live trading
4. **File Management**: Download models for external use

## 🚨 Monitoring and Troubleshooting

### Worker Health Monitoring

```bash
# Check worker status
curl http://orion:8000/api/workers

# View specific worker details
curl http://orion:8000/api/workers/{worker_id}

# Check experiment queue
curl http://orion:8000/api/experiments?status=pending
```

### Resource Monitoring

```bash
# On each worker machine
python manage_resources.py info
python manage_resources.py benchmark
```

### Log Files

- **GENISYS-WEB**: `logs/genisys-web.log`
- **Workers**: Console output or redirect to files
- **PostgreSQL**: Docker container logs

### Common Issues

**Worker Connection Issues:**
```bash
# Check network connectivity
ping orion
curl http://orion:8000/health

# Verify worker registration
tail -f worker.log
```

**Memory Issues:**
```bash
# Check current usage
python manage_resources.py info

# Reduce resource usage
python manage_resources.py profile laptop_16gb  # Most conservative
```

**Database Issues:**
```bash
# Check PostgreSQL container
docker ps | grep postgres
docker logs <postgres_container>

# Test database connection
psql postgresql://genisys:genisys_password@localhost:5432/genisys_web
```

## 🔒 Security Considerations

### Production Deployment

1. **Change Default Passwords**: Update database and application secrets
2. **Network Security**: Configure firewall rules between machines
3. **HTTPS**: Use reverse proxy (nginx) with SSL certificates
4. **Authentication**: Implement user authentication for web interface
5. **File Permissions**: Secure model storage directories

### Development vs Production

Current setup is optimized for development/research. For production:

- Use Docker containers for easier deployment
- Implement proper logging and monitoring
- Add backup strategies for database and models
- Configure high availability for critical components

## 📈 Performance Expectations

### Experiment Execution Times

**Data Processing (create_master_dataset.py):**
- ORION (64GB): ~25-35 minutes for full dataset
- APOLLO (128GB): ~15-20 minutes for full dataset

**ML Training and Backtesting:**
- ORION: ~8-15 minutes per experiment
- APOLLO: ~5-10 minutes per experiment

**Parameter Sweeps:**
- 100 experiments: ~8-25 hours (depending on worker allocation)
- 1000 experiments: ~3-10 days (distributed across both workers)

### Concurrent Execution

- **ORION**: 1 experiment at a time (conservative for server stability)
- **APOLLO**: Up to 2-4 experiments concurrently (depending on memory usage)
- **Total System**: 2-5 concurrent experiments

## 🎯 Next Steps

1. **Deploy and Test**: Start with single experiments to verify setup
2. **Parameter Optimization**: Run systematic parameter sweeps
3. **Model Validation**: Test models across different time periods
4. **Production Integration**: Connect best models to live trading system
5. **Scaling**: Add more worker machines as needed

The distributed GENISYS system provides a robust platform for systematic trading strategy research with automatic resource optimization and comprehensive result tracking.
