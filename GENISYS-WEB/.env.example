# GENISYS-WEB Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Database Configuration (Update for your PostgreSQL container)
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Security (Generate secure keys for production)
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
MODEL_STORAGE_PATH=./models
MAX_MODEL_FILE_SIZE=1073741824

# Worker Configuration
WORKER_HEARTBEAT_TIMEOUT=300
MAX_CONCURRENT_EXPERIMENTS_PER_WORKER=4

# Experiment Configuration
DEFAULT_EXPERIMENT_TIMEOUT=86400
MAX_PARAMETER_COMBINATIONS=10000

# Redis Configuration (for background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/genisys-web.log
