# GENISYS-WEB Next Session Development Plan

## 🎯 **Session Goal: Fix Web Interface & Enable Experiment Submission**

### **Current Status**
- ✅ FastAPI server running on ORION (***********:9999)
- ✅ 2 workers connected (ORION + APOLLO)
- ✅ Worker registration and heartbeat working
- ⚠️ Web pages returning 500 internal server errors
- ⚠️ Experiment submission not implemented

## 🔧 **Step-by-Step Development Plan**

### **Step 1: Debug 500 Errors (30 minutes)**

**1.1 Enable Debug Mode**
```bash
# Edit .env file
echo "DEBUG=true" >> .env

# Start server with verbose logging
python start_server.py
```

**1.2 Check Database Tables**
```python
# Run this in Python console
from app.database import engine, Base
from app.models import Worker, Experiment, ExperimentResult

# Check if tables exist
inspector = sqlalchemy.inspect(engine)
tables = inspector.get_table_names()
print("Existing tables:", tables)

# Create tables if missing
Base.metadata.create_all(bind=engine)
```

**1.3 Test Template Rendering**
```python
# Test dashboard template
from fastapi.templating import Jinja2Templates
from fastapi import Request

templates = Jinja2Templates(directory="app/templates")
# Check if this works without errors
```

### **Step 2: Fix Dashboard Page (45 minutes)**

**2.1 Simplify Dashboard Template**
- Remove complex JavaScript initially
- Test basic HTML rendering
- Add error handling for missing data

**2.2 Test Database Queries**
```python
# Test worker query in dashboard
from app.database import SessionLocal
from app.models import Worker

db = SessionLocal()
workers = db.query(Worker).all()
print(f"Found {len(workers)} workers")
```

**2.3 Add Error Handling**
```python
# In dashboard route
@app.get("/")
async def dashboard(request: Request):
    try:
        # Database queries here
        return templates.TemplateResponse("dashboard.html", {"request": request})
    except Exception as e:
        return {"error": str(e)}
```

### **Step 3: Implement Basic Experiment Submission (60 minutes)**

**3.1 Create Simple Experiment Form**
```html
<!-- In templates/experiment_builder.html -->
<form method="post" action="/api/experiments/submit">
    <select name="timeframes">
        <option value="M1">1 Minute</option>
        <option value="M5">5 Minutes</option>
        <option value="H1">1 Hour</option>
    </select>
    
    <select name="indicators">
        <option value="EMA">EMA</option>
        <option value="RSI">RSI</option>
    </select>
    
    <input type="number" name="confidence_threshold" value="0.6" step="0.1">
    <button type="submit">Submit Experiment</button>
</form>
```

**3.2 Implement Experiment Submission API**
```python
# In app/routers/experiments.py
@router.post("/submit")
async def submit_experiment(
    timeframes: str = Form(...),
    indicators: str = Form(...),
    confidence_threshold: float = Form(...),
    db: Session = Depends(get_db)
):
    # Create experiment config
    config = {
        "experiment_name": f"Test_{int(time.time())}",
        "strategy": {
            "type": "ml_confidence",
            "base_timeframe": timeframes,
            "feature_set": [f"{timeframes}_ASK_CLOSE", f"{timeframes}_{indicators}_14"],
            "model_params": {"n_estimators": 100},
            "trade_params": {"confidence_threshold": confidence_threshold}
        },
        "data": {"source_file": "GENISYS_EURUSD_MASTER.parquet"}
    }
    
    # Store in database
    experiment = Experiment(
        name=config["experiment_name"],
        strategy_config=config,
        status="pending"
    )
    db.add(experiment)
    db.commit()
    
    return {"message": "Experiment submitted", "experiment_id": experiment.id}
```

**3.3 Implement Job Distribution**
```python
# In app/routers/workers.py
@router.get("/{worker_id}/jobs")
async def get_worker_jobs(worker_id: int, db: Session = Depends(get_db)):
    # Find pending experiments
    pending_experiments = db.query(Experiment).filter(
        Experiment.status == "pending",
        Experiment.worker_id.is_(None)
    ).limit(1).all()
    
    if pending_experiments:
        experiment = pending_experiments[0]
        experiment.worker_id = worker_id
        experiment.status = "assigned"
        db.commit()
        
        return [{
            "experiment_id": experiment.id,
            "config": experiment.strategy_config
        }]
    
    return []
```

### **Step 4: Test End-to-End Flow (30 minutes)**

**4.1 Submit Test Experiment**
```bash
# Via web interface
curl -X POST http://localhost:9999/api/experiments/submit \
  -F "timeframes=M1" \
  -F "indicators=EMA" \
  -F "confidence_threshold=0.6"
```

**4.2 Verify Worker Picks Up Job**
- Check worker logs for job polling
- Verify experiment status changes to "assigned"
- Monitor worker execution

**4.3 Check Result Submission**
- Worker should submit results via API
- Results should be stored in database
- Web interface should display results

### **Step 5: Basic Results Display (45 minutes)**

**5.1 Implement Results API**
```python
# In app/routers/results.py
@router.get("/")
async def list_results(db: Session = Depends(get_db)):
    results = db.query(ExperimentResult).join(Experiment).all()
    return [
        {
            "experiment_name": result.experiment.name,
            "sharpe_ratio": result.sharpe_ratio,
            "total_return": result.total_return,
            "total_trades": result.total_trades
        }
        for result in results
    ]
```

**5.2 Create Results Page**
```html
<!-- In templates/results_analysis.html -->
<table class="table">
    <thead>
        <tr>
            <th>Experiment</th>
            <th>Sharpe Ratio</th>
            <th>Total Return</th>
            <th>Trades</th>
        </tr>
    </thead>
    <tbody id="results-table">
        <!-- Populated via JavaScript -->
    </tbody>
</table>
```

## 🧪 **Testing Checklist**

### **Basic Functionality**
- [ ] Web server starts without errors
- [ ] Dashboard loads successfully
- [ ] Workers visible in dashboard
- [ ] Experiment form submits successfully
- [ ] Worker receives and executes job
- [ ] Results are stored and displayed

### **Error Handling**
- [ ] Database connection failures handled gracefully
- [ ] Template rendering errors caught
- [ ] API validation errors returned properly
- [ ] Worker disconnection handled

### **Performance**
- [ ] Page load times acceptable
- [ ] Database queries optimized
- [ ] Worker polling frequency appropriate
- [ ] Memory usage stable

## 🔧 **Development Environment Setup**

```bash
# SSH to ORION
ssh user@***********

# Navigate to project
cd /DATA/CODE/GENISYS/GENISYS-WEB

# Activate environment
source .venv/bin/activate

# Start development server
DEBUG=true python start_server.py

# In another terminal, monitor logs
tail -f logs/genisys-web.log

# Test API endpoints
curl http://localhost:9999/api/workers/
curl http://localhost:9999/health
```

## 📊 **Success Metrics for Session**

### **Minimum Viable Product (MVP)**
1. ✅ Web interface loads without 500 errors
2. ✅ Can submit basic experiment via web form
3. ✅ Worker receives and executes experiment
4. ✅ Results are stored in database
5. ✅ Results are visible in web interface

### **Stretch Goals**
1. Real-time progress monitoring
2. Multiple experiment submission
3. Parameter sweep functionality
4. Enhanced error handling
5. Improved UI/UX

## 🚨 **Common Issues & Solutions**

### **Database Issues**
```sql
-- Check table existence
\dt

-- Recreate tables if needed
DROP TABLE IF EXISTS workers, experiments, experiment_results CASCADE;
-- Then restart server to recreate
```

### **Template Issues**
```python
# Test template rendering
from jinja2 import Template
template = Template("Hello {{ name }}!")
result = template.render(name="World")
```

### **Worker Connection Issues**
```bash
# Check worker status
curl http://localhost:9999/api/workers/

# Restart worker if needed
python worker_daemon.py --server-url http://localhost:9999 --worker-name TEST
```

## 🎯 **Next Session Outcome**

By the end of the next session, you should have:
- ✅ Fully functional web interface
- ✅ Working experiment submission
- ✅ End-to-end experiment execution
- ✅ Basic results visualization
- ✅ Foundation for advanced features

**This will complete the MVP of your distributed trading research platform!** 🚀
