# GENISYS-WEB Quick Start Guide

Get your GENISYS trading research web server running in 5 minutes!

## 🎯 What is GENISYS-WEB?

GENISYS-WEB is a **standalone FastAPI web server** that provides:
- 🌐 Web interface for configuring trading strategies
- 📊 Real-time monitoring of distributed experiments
- 📈 Results analysis and performance ranking
- 🤖 Model repository for production deployment
- 🔧 Worker management across multiple machines

## ⚡ 5-Minute Setup

### Step 1: Install Dependencies
```bash
# Ensure you're in the GENISYS-WEB directory
cd GENISYS-WEB

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install all dependencies
pip install -r requirements.txt
```

### Step 2: Configure Database
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your database settings
nano .env  # or use your preferred editor
```

**Update these lines in `.env`:**
```env
# Database Configuration (update for your PostgreSQL)
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Change this for production
SECRET_KEY=your-secure-secret-key-here
```

### Step 3: Setup Database
```bash
# Create the database (run this in PostgreSQL)
# psql -U postgres -c "CREATE DATABASE genisys_web;"
# psql -U postgres -c "CREATE USER genisys WITH PASSWORD 'genisys_password';"
# psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;"

# Or if you have Docker PostgreSQL:
# docker exec -it <postgres_container> psql -U postgres -c "CREATE DATABASE genisys_web;"
```

### Step 4: Fix Common Issues (if needed)
```bash
# If you encounter configuration errors, run:
python fix_setup.py
```

### Step 5: Test Setup
```bash
# Run setup test
python test_setup.py
```

### Step 6: Start Web Server
```bash
# Option 1: Use the startup script (recommended)
python start_server.py

# Option 2: Start manually
python -m app.main

# Server will start on http://localhost:8000
```

## 🌐 Access Your Web Interface

Open your browser and go to:
- **Dashboard**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Connect Workers

To connect GENISYS-ANALYTIC workers to your web server:

```bash
# On each worker machine, run:
cd /path/to/GENISYS-ANALYTIC
python worker_daemon.py --server-url http://your-server-ip:8000 --worker-name WORKER_NAME
```

## 📊 Web Interface Features

### Dashboard
- Real-time worker status
- Experiment progress monitoring
- System resource usage
- Quick action buttons

### Strategy Builder
- Visual timeframe selection (S5, M1, H1, D, etc.)
- Indicator configuration (EMA, RSI, ADX, MACD)
- Parameter range definition
- Batch experiment generation

### Results Analysis
- Performance ranking by Sharpe ratio
- Interactive charts and visualizations
- Statistical analysis tools
- Model performance tracking

### Worker Management
- Worker registration and status
- Resource usage monitoring
- Job queue management
- Health monitoring

## 🚨 Troubleshooting

### Configuration Errors (Pydantic validation)
```bash
# Fix common configuration issues
python fix_setup.py

# This fixes:
# - Extra inputs not permitted errors
# - Missing .env file
# - Directory creation
```

### Database Connection Issues
```bash
# Test database connection
python -c "
from app.database import engine
try:
    with engine.connect() as conn:
        print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database error: {e}')
"
```

### Import Errors
```bash
# Ensure virtual environment is activated
source .venv/bin/activate

# Reinstall dependencies
pip install -r requirements.txt
```

### Port Already in Use
```bash
# Change port in .env file
echo "PORT=8001" >> .env

# Or kill existing process
lsof -ti:8000 | xargs kill -9
```

### Worker Connection Issues
```bash
# Check if web server is accessible
curl http://localhost:8000/health

# Check firewall settings
# Ensure port 8000 is open for worker connections
```

## 🔧 Configuration Options

### Environment Variables (.env)
```env
# Server Configuration
HOST=0.0.0.0          # Listen on all interfaces
PORT=8000              # Web server port
DEBUG=false            # Set to true for development

# Database
DATABASE_URL=postgresql://user:pass@host:port/dbname

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
MODEL_STORAGE_PATH=./models
MAX_MODEL_FILE_SIZE=**********  # 1GB

# Worker Settings
WORKER_HEARTBEAT_TIMEOUT=300
MAX_CONCURRENT_EXPERIMENTS_PER_WORKER=4
```

### Docker Deployment (Optional)
```bash
# Use Docker Compose for production
docker-compose up -d

# View logs
docker-compose logs -f
```

## 📈 Next Steps

1. **Connect Workers**: Set up GENISYS-ANALYTIC workers on your machines
2. **Create Experiments**: Use the web interface to configure strategies
3. **Monitor Progress**: Watch real-time execution across workers
4. **Analyze Results**: Use built-in tools to find profitable strategies
5. **Deploy Models**: Mark best performers for live trading

## 🆘 Need Help?

- **Setup Issues**: Run `python test_setup.py` for diagnostics
- **API Documentation**: Visit `/api/docs` for complete API reference
- **Database Issues**: Check PostgreSQL connection and permissions
- **Worker Issues**: Ensure network connectivity between machines

## 🎉 You're Ready!

Your GENISYS-WEB server is now running and ready to coordinate distributed trading strategy research across your infrastructure!

**Happy trading strategy research!** 🚀
