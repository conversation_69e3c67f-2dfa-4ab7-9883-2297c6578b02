# GENISYS-WEB: Trading Strategy Research Platform

**Standalone FastAPI web server** for distributed algorithmic trading research. Provides a professional web interface for configuring strategies, managing experiments, and analyzing results across multiple worker machines.

## 🎯 What is GENISYS-WEB?

GENISYS-WEB is the **control center** for your distributed trading research infrastructure:

- 🌐 **Web Dashboard**: Real-time monitoring and control
- 🎛️ **Strategy Builder**: Visual configuration of trading strategies
- 📊 **Results Analysis**: Performance ranking and statistical analysis
- 🤖 **Model Repository**: Trained model management for production
- 🔧 **Worker Coordination**: Manage multiple analysis machines

## 🏗️ System Architecture

```
┌─────────────────────────────────────┐
│           GENISYS-WEB               │
│        (This Application)           │
│  ┌─────────────────────────────────┐│
│  │  FastAPI Web Server             ││
│  │  PostgreSQL Database            ││
│  │  Strategy Configuration GUI     ││
│  │  Results Visualization          ││
│  └─────────────────────────────────┘│
└─────────────────┬───────────────────┘
                  │ REST API
    ┌─────────────┼─────────────┐
    │             │             │
    ▼             ▼             ▼
┌─────────┐  ┌─────────┐  ┌─────────┐
│Worker 1 │  │Worker 2 │  │Worker N │
│GENISYS- │  │GENISYS- │  │GENISYS- │
│ANALYTIC │  │ANALYTIC │  │ANALYTIC │
└─────────┘  └─────────┘  └─────────┘
```

## ⚡ Quick Start

> **New to GENISYS-WEB?** See [QUICKSTART.md](QUICKSTART.md) for 5-minute setup!

### 1. Install and Configure

```bash
# Setup virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure database connection
cp .env.example .env
# Edit .env with your PostgreSQL settings
```

### 2. Setup Database

```bash
# Create PostgreSQL database
createdb genisys_web
# Or: docker exec postgres createdb -U postgres genisys_web
```

### 3. Start Web Server

```bash
# Test setup first
python test_setup.py

# Start the server
python -m app.main

# Access at: http://localhost:8000
```

### 4. Connect Workers

Connect GENISYS-ANALYTIC workers from any machine:

```bash
# On each worker machine
python worker_daemon.py --server-url http://your-server:8000
```

## 📊 Features

### Strategy Builder
- Visual timeframe selection
- Drag-and-drop indicator configuration
- Parameter range definition
- Resource requirement estimation

### Experiment Management
- Batch experiment submission
- Parameter sweep generation
- Worker load balancing
- Real-time progress monitoring

### Results Analysis
- Performance ranking and comparison
- Statistical significance testing
- Interactive equity curves
- Parameter correlation analysis

### Model Repository
- Trained model storage and versioning
- Performance tracking across periods
- Production deployment marking
- Model file management

## 🔧 Configuration

### Database Setup

Create a PostgreSQL database for GENISYS-WEB:

```sql
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;
```

### Worker Configuration

Each GENISYS-ANALYTIC worker automatically detects its resources and registers with the web server. Manual configuration available in `resource_config.json`.

## 🚀 Deployment

### Development (Native)
```bash
python -m app.main
```

### Production (Docker)
```bash
docker-compose up -d
```

## 📁 Project Structure

```
GENISYS-WEB/
├── app/                    # FastAPI application
│   ├── models/            # Database ORM models
│   ├── schemas/           # Pydantic API schemas
│   ├── routers/           # API route handlers
│   ├── services/          # Business logic
│   ├── templates/         # HTML templates
│   └── static/            # CSS, JS, images
├── models/                # Trained ML model storage
├── logs/                  # Application logs
└── tests/                 # Test suite
```

## 🔗 API Documentation

Once running, visit:
- Swagger UI: `http://orion:8000/api/docs`
- ReDoc: `http://orion:8000/api/redoc`

## 🤝 Integration with GENISYS-ANALYTIC

The web server communicates with worker instances via REST API:
- Worker registration and heartbeat
- Experiment job distribution
- Real-time progress updates
- Result collection and storage
- Model file transfer

For more details, see `GENISYS_WEB_ARCHITECTURE.md`.
