# GENISYS-WEB: Trading Strategy Research Platform

FastAPI-based web interface for the distributed GENISYS trading system. Provides strategy configuration, experiment management, and results analysis across multiple worker machines.

## 🏗️ Architecture

- **GENISYS-WEB**: FastAPI server for web interface and API (runs on ORION)
- **GENISYS-ANALYTIC**: Worker instances for strategy execution (runs on ORION + APOLLO)
- **PostgreSQL**: Centralized database for experiments and results
- **Redis**: Background task queue and caching

## 🚀 Quick Start

### 1. Setup on ORION (64GB Server)

```bash
# Clone and setup
git clone <repository-url>
cd GENISYS-WEB

# Create virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL connection details

# Initialize database
alembic upgrade head

# Start the web server
python -m app.main
```

### 2. Setup Workers

On each machine (ORION and APOLLO), run the GENISYS-ANALYTIC worker:

```bash
cd GENISYS-ANALY<PERSON>C
python worker_daemon.py --server-url http://orion:8000
```

### 3. Access Web Interface

Open your browser to: `http://orion:8000`

## 📊 Features

### Strategy Builder
- Visual timeframe selection
- Drag-and-drop indicator configuration
- Parameter range definition
- Resource requirement estimation

### Experiment Management
- Batch experiment submission
- Parameter sweep generation
- Worker load balancing
- Real-time progress monitoring

### Results Analysis
- Performance ranking and comparison
- Statistical significance testing
- Interactive equity curves
- Parameter correlation analysis

### Model Repository
- Trained model storage and versioning
- Performance tracking across periods
- Production deployment marking
- Model file management

## 🔧 Configuration

### Database Setup

Create a PostgreSQL database for GENISYS-WEB:

```sql
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;
```

### Worker Configuration

Each GENISYS-ANALYTIC worker automatically detects its resources and registers with the web server. Manual configuration available in `resource_config.json`.

## 🚀 Deployment

### Development (Native)
```bash
python -m app.main
```

### Production (Docker)
```bash
docker-compose up -d
```

## 📁 Project Structure

```
GENISYS-WEB/
├── app/                    # FastAPI application
│   ├── models/            # Database ORM models
│   ├── schemas/           # Pydantic API schemas
│   ├── routers/           # API route handlers
│   ├── services/          # Business logic
│   ├── templates/         # HTML templates
│   └── static/            # CSS, JS, images
├── models/                # Trained ML model storage
├── logs/                  # Application logs
└── tests/                 # Test suite
```

## 🔗 API Documentation

Once running, visit:
- Swagger UI: `http://orion:8000/api/docs`
- ReDoc: `http://orion:8000/api/redoc`

## 🤝 Integration with GENISYS-ANALYTIC

The web server communicates with worker instances via REST API:
- Worker registration and heartbeat
- Experiment job distribution
- Real-time progress updates
- Result collection and storage
- Model file transfer

For more details, see `GENISYS_WEB_ARCHITECTURE.md`.
