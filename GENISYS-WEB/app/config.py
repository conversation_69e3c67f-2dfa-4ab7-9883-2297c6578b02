"""
Configuration management for GENISYS-WEB application.
"""

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Server configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False

    # Database configuration
    DATABASE_URL: str = "postgresql://genisys:genisys_password@localhost:5432/genisys_web"

    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # File storage
    MODEL_STORAGE_PATH: str = "./models"
    MAX_MODEL_FILE_SIZE: int = 1024 * 1024 * 1024  # 1GB

    # Worker communication
    WORKER_HEARTBEAT_TIMEOUT: int = 300  # 5 minutes
    MAX_CONCURRENT_EXPERIMENTS_PER_WORKER: int = 4

    # Experiment configuration
    DEFAULT_EXPERIMENT_TIMEOUT: int = 86400  # 24 hours
    MAX_PARAMETER_COMBINATIONS: int = 10000

    # Redis configuration (optional)
    REDIS_URL: str = "redis://localhost:6379/0"

    # Logging configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/genisys-web.log"

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Allow extra fields in .env file

settings = Settings()
