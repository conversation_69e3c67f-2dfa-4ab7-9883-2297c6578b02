"""
GENISYS-WEB: FastAPI Trading Strategy Research Platform

Main application entry point for the distributed GENISYS trading system.
Provides web interface for strategy configuration, experiment management,
and results analysis across multiple worker machines.
"""

from fastapi import FastAPI, Request
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.database import engine, Base
from app.routers import workers, experiments, results, strategies, models
from app.config import settings

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI application
app = FastAPI(
    title="GENISYS Trading Research Platform",
    description="Distributed trading strategy research and backtesting system",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Add CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Initialize templates
templates = Jinja2Templates(directory="app/templates")

# Include API routers
app.include_router(workers.router, prefix="/api/workers", tags=["workers"])
app.include_router(experiments.router, prefix="/api/experiments", tags=["experiments"])
app.include_router(results.router, prefix="/api/results", tags=["results"])
app.include_router(strategies.router, prefix="/api/strategies", tags=["strategies"])
app.include_router(models.router, prefix="/api/models", tags=["models"])

@app.get("/")
async def dashboard(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/experiment-builder")
async def experiment_builder(request: Request):
    """Strategy configuration and experiment builder page."""
    return templates.TemplateResponse("experiment_builder.html", {"request": request})

@app.get("/results")
async def results_page(request: Request):
    """Results analysis and visualization page."""
    return templates.TemplateResponse("results_analysis.html", {"request": request})

@app.get("/workers")
async def workers_page(request: Request):
    """Worker management and monitoring page."""
    return templates.TemplateResponse("worker_status.html", {"request": request})

@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "genisys-web"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
