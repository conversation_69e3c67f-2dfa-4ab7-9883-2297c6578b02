"""
Model repository API endpoints.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.database import get_db

router = APIRouter()

@router.get("/")
async def list_models(db: Session = Depends(get_db)):
    """List all models in repository."""
    # TODO: Implement model listing
    return []

@router.get("/{model_id}")
async def get_model(model_id: int, db: Session = Depends(get_db)):
    """Get specific model details."""
    # TODO: Implement model retrieval
    return {"model_id": model_id, "message": "TODO"}

@router.post("/{model_id}/mark-production")
async def mark_model_production(model_id: int, db: Session = Depends(get_db)):
    """Mark model as production ready."""
    # TODO: Implement production marking
    return {"message": "Model production marking endpoint - TODO"}
