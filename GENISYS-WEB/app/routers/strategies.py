"""
Strategy configuration API endpoints.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.database import get_db

router = APIRouter()

@router.get("/templates")
async def list_strategy_templates(db: Session = Depends(get_db)):
    """List available strategy templates."""
    # TODO: Implement strategy templates
    return []

@router.get("/timeframes")
async def list_timeframes():
    """List available timeframes."""
    return [
        {"name": "S5", "description": "5 seconds"},
        {"name": "S10", "description": "10 seconds"},
        {"name": "S15", "description": "15 seconds"},
        {"name": "S30", "description": "30 seconds"},
        {"name": "M1", "description": "1 minute"},
        {"name": "M2", "description": "2 minutes"},
        {"name": "M5", "description": "5 minutes"},
        {"name": "M10", "description": "10 minutes"},
        {"name": "M15", "description": "15 minutes"},
        {"name": "M30", "description": "30 minutes"},
        {"name": "H1", "description": "1 hour"},
        {"name": "H4", "description": "4 hours"},
        {"name": "D", "description": "1 day"}
    ]

@router.get("/indicators")
async def list_indicators():
    """List available technical indicators."""
    return [
        {"name": "EMA", "description": "Exponential Moving Average"},
        {"name": "RSI", "description": "Relative Strength Index"},
        {"name": "ADX", "description": "Average Directional Index"},
        {"name": "MACD", "description": "Moving Average Convergence Divergence"},
        {"name": "BBands", "description": "Bollinger Bands"},
        {"name": "ATR", "description": "Average True Range"}
    ]
