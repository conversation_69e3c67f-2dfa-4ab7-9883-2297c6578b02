"""
Worker management API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, timezone

from app.database import get_db
from app.models import Worker
from app.schemas.worker import WorkerCreate, WorkerResponse, WorkerHeartbeat

router = APIRouter()

@router.post("/register", response_model=WorkerResponse)
async def register_worker(worker_data: WorkerCreate, db: Session = Depends(get_db)):
    """Register a new worker or update existing worker."""
    
    # Check if worker already exists
    existing_worker = db.query(Worker).filter(Worker.name == worker_data.name).first()
    
    if existing_worker:
        # Update existing worker
        existing_worker.hostname = worker_data.hostname
        existing_worker.ip_address = worker_data.ip_address
        existing_worker.resource_config = worker_data.resource_config
        existing_worker.capabilities = worker_data.capabilities
        existing_worker.status = "online"
        existing_worker.last_heartbeat = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(existing_worker)
        
        return {"worker_id": existing_worker.id, "message": "Worker updated successfully"}
    
    else:
        # Create new worker
        new_worker = Worker(
            name=worker_data.name,
            hostname=worker_data.hostname,
            ip_address=worker_data.ip_address,
            resource_config=worker_data.resource_config,
            capabilities=worker_data.capabilities,
            status="online",
            last_heartbeat=datetime.now(timezone.utc)
        )
        
        db.add(new_worker)
        db.commit()
        db.refresh(new_worker)
        
        return {"worker_id": new_worker.id, "message": "Worker registered successfully"}

@router.post("/{worker_id}/heartbeat")
async def worker_heartbeat(worker_id: int, heartbeat_data: WorkerHeartbeat, db: Session = Depends(get_db)):
    """Update worker heartbeat and status."""
    
    worker = db.query(Worker).filter(Worker.id == worker_id).first()
    if not worker:
        raise HTTPException(status_code=404, detail="Worker not found")
    
    worker.status = heartbeat_data.status
    worker.last_heartbeat = datetime.now(timezone.utc)
    
    db.commit()
    
    return {"message": "Heartbeat received"}

@router.get("/{worker_id}/jobs")
async def get_worker_jobs(worker_id: int, db: Session = Depends(get_db)):
    """Get pending jobs for a worker."""
    
    worker = db.query(Worker).filter(Worker.id == worker_id).first()
    if not worker:
        raise HTTPException(status_code=404, detail="Worker not found")
    
    # TODO: Implement job queue logic
    # For now, return empty list
    return []

@router.get("/", response_model=List[dict])
async def list_workers(db: Session = Depends(get_db)):
    """List all registered workers."""
    
    workers = db.query(Worker).all()
    
    return [
        {
            "id": worker.id,
            "name": worker.name,
            "hostname": worker.hostname,
            "ip_address": worker.ip_address,
            "status": worker.status,
            "last_heartbeat": worker.last_heartbeat,
            "resource_config": worker.resource_config,
            "capabilities": worker.capabilities
        }
        for worker in workers
    ]

@router.get("/{worker_id}")
async def get_worker(worker_id: int, db: Session = Depends(get_db)):
    """Get specific worker details."""
    
    worker = db.query(Worker).filter(Worker.id == worker_id).first()
    if not worker:
        raise HTTPException(status_code=404, detail="Worker not found")
    
    return {
        "id": worker.id,
        "name": worker.name,
        "hostname": worker.hostname,
        "ip_address": worker.ip_address,
        "status": worker.status,
        "last_heartbeat": worker.last_heartbeat,
        "resource_config": worker.resource_config,
        "capabilities": worker.capabilities,
        "created_at": worker.created_at
    }

@router.post("/{worker_id}/shutdown")
async def worker_shutdown(worker_id: int, db: Session = Depends(get_db)):
    """Mark worker as offline."""
    
    worker = db.query(Worker).filter(Worker.id == worker_id).first()
    if not worker:
        raise HTTPException(status_code=404, detail="Worker not found")
    
    worker.status = "offline"
    db.commit()
    
    return {"message": "Worker marked as offline"}
