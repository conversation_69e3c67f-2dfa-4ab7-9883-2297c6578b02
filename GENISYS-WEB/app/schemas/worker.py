"""
Pydantic schemas for worker API.
"""

from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class WorkerCreate(BaseModel):
    """Schema for worker registration."""
    name: str
    hostname: str
    ip_address: str
    resource_config: Dict[str, Any]
    capabilities: Dict[str, Any]

class WorkerResponse(BaseModel):
    """Schema for worker registration response."""
    worker_id: int
    message: str

class WorkerHeartbeat(BaseModel):
    """Schema for worker heartbeat."""
    status: str
    current_experiment_id: Optional[str] = None
    resource_usage: Optional[Dict[str, Any]] = None
    uptime_seconds: Optional[int] = None

class WorkerStatus(BaseModel):
    """Schema for worker status."""
    id: int
    name: str
    hostname: str
    ip_address: str
    status: str
    last_heartbeat: Optional[datetime]
    resource_config: Dict[str, Any]
    capabilities: Dict[str, Any]
    
    class Config:
        from_attributes = True
