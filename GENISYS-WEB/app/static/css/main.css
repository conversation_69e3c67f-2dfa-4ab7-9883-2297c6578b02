/* GENISYS-WEB Custom Styles */

body {
    background-color: #f8f9fa;
}

.navbar-brand {
    font-size: 1.5rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Status indicators */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #6c757d;
}

.status-busy {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

/* Progress bars */
.progress {
    height: 1.5rem;
}

/* Experiment builder styles */
.timeframe-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.timeframe-option {
    text-align: center;
    padding: 10px;
    border: 2px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeframe-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.timeframe-option.selected {
    border-color: #007bff;
    background-color: #007bff;
    color: white;
}

/* Indicator builder */
.indicator-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
}

.indicator-item {
    padding: 8px 16px;
    background-color: #e9ecef;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator-item:hover {
    background-color: #007bff;
    color: white;
}

.indicator-item.selected {
    background-color: #28a745;
    color: white;
}

/* Results table */
.results-table {
    font-size: 0.9rem;
}

.results-table .sharpe-positive {
    color: #28a745;
    font-weight: bold;
}

.results-table .sharpe-negative {
    color: #dc3545;
    font-weight: bold;
}

/* Worker status */
.worker-card {
    border-left: 4px solid #dee2e6;
}

.worker-card.online {
    border-left-color: #28a745;
}

.worker-card.offline {
    border-left-color: #6c757d;
}

.worker-card.busy {
    border-left-color: #ffc107;
}

/* Resource usage indicators */
.resource-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.resource-bar-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.resource-bar-fill.low {
    background-color: #28a745;
}

.resource-bar-fill.medium {
    background-color: #ffc107;
}

.resource-bar-fill.high {
    background-color: #fd7e14;
}

.resource-bar-fill.critical {
    background-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timeframe-selector {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }
    
    .card-body {
        padding: 1rem 0.75rem;
    }
}

/* Animation for loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom button styles */
.btn-experiment {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-experiment:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
    color: white;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Utility classes */
.text-sharpe-positive {
    color: #28a745 !important;
}

.text-sharpe-negative {
    color: #dc3545 !important;
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #117a8b);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}
