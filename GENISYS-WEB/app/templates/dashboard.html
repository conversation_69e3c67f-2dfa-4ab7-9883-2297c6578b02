{% extends "base.html" %}

{% block title %}Dashboard - GENISYS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">GENISYS Trading Research Dashboard</h1>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Active Workers</h5>
                <h2 class="card-text" id="active-workers">-</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Running Experiments</h5>
                <h2 class="card-text" id="running-experiments">-</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">Completed Today</h5>
                <h2 class="card-text" id="completed-today">-</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <h5 class="card-title">Queued Jobs</h5>
                <h2 class="card-text" id="queued-jobs">-</h2>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="/experiment-builder" class="btn btn-primary btn-lg w-100 mb-2">
                            <i class="fas fa-plus"></i> Create New Experiment
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/results" class="btn btn-success btn-lg w-100 mb-2">
                            <i class="fas fa-chart-line"></i> View Results
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/workers" class="btn btn-info btn-lg w-100 mb-2">
                            <i class="fas fa-server"></i> Manage Workers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Experiments</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Worker</th>
                                <th>Progress</th>
                                <th>Started</th>
                            </tr>
                        </thead>
                        <tbody id="recent-experiments">
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    Loading experiments...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Worker Status</h5>
            </div>
            <div class="card-body">
                <div id="worker-status">
                    <p class="text-muted">Loading worker status...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard data
    loadDashboardData();
    
    // Refresh data every 30 seconds
    setInterval(loadDashboardData, 30000);
});

async function loadDashboardData() {
    try {
        // Load workers
        const workersResponse = await fetch('/api/workers/');
        const workers = await workersResponse.json();
        
        // Update worker count
        const activeWorkers = workers.filter(w => w.status === 'online').length;
        document.getElementById('active-workers').textContent = activeWorkers;
        
        // Update worker status display
        updateWorkerStatus(workers);
        
        // Load experiments (placeholder)
        document.getElementById('running-experiments').textContent = '0';
        document.getElementById('completed-today').textContent = '0';
        document.getElementById('queued-jobs').textContent = '0';
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateWorkerStatus(workers) {
    const workerStatusDiv = document.getElementById('worker-status');
    
    if (workers.length === 0) {
        workerStatusDiv.innerHTML = '<p class="text-muted">No workers registered</p>';
        return;
    }
    
    let html = '';
    workers.forEach(worker => {
        const statusClass = worker.status === 'online' ? 'success' : 'secondary';
        const lastSeen = worker.last_heartbeat ? 
            new Date(worker.last_heartbeat).toLocaleString() : 'Never';
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <strong>${worker.name}</strong>
                    <br>
                    <small class="text-muted">${worker.hostname}</small>
                </div>
                <span class="badge bg-${statusClass}">${worker.status}</span>
            </div>
        `;
    });
    
    workerStatusDiv.innerHTML = html;
}
</script>
{% endblock %}
