version: '3.8'

services:
  genisys-web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************************/genisys_web
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:17
    environment:
      POSTGRES_DB: genisys_web
      POSTGRES_USER: genisys
      POSTGRES_PASSWORD: genisys_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
