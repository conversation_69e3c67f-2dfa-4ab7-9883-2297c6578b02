#!/usr/bin/env python
"""
GENISYS-WEB Setup Fix Script

Fixes common setup issues and ensures the application can start properly.
"""

import sys
from pathlib import Path

def fix_env_file():
    """Fix .env file to remove problematic entries."""
    print("🔧 Fixing .env file...")
    
    env_path = Path(".env")
    if not env_path.exists():
        print("⚠️  .env file not found. Creating from template...")
        template_path = Path(".env.example")
        if template_path.exists():
            # Copy template and remove problematic lines
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Remove the problematic lines
            lines = content.split('\n')
            filtered_lines = []
            skip_lines = ['REDIS_URL=', 'LOG_LEVEL=', 'LOG_FILE=']
            
            for line in lines:
                if not any(line.startswith(skip) for skip in skip_lines):
                    filtered_lines.append(line)
            
            with open(env_path, 'w') as f:
                f.write('\n'.join(filtered_lines))
            
            print("✅ Created .env file from template")
        else:
            # Create minimal .env file
            minimal_env = """# GENISYS-WEB Environment Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Security
SECRET_KEY=genisys-development-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
MODEL_STORAGE_PATH=./models
MAX_MODEL_FILE_SIZE=1073741824

# Worker Configuration
WORKER_HEARTBEAT_TIMEOUT=300
MAX_CONCURRENT_EXPERIMENTS_PER_WORKER=4

# Experiment Configuration
DEFAULT_EXPERIMENT_TIMEOUT=86400
MAX_PARAMETER_COMBINATIONS=10000
"""
            with open(env_path, 'w') as f:
                f.write(minimal_env)
            print("✅ Created minimal .env file")
    else:
        # Fix existing .env file
        with open(env_path, 'r') as f:
            content = f.read()
        
        # Remove problematic lines
        lines = content.split('\n')
        filtered_lines = []
        skip_lines = ['REDIS_URL=', 'LOG_LEVEL=', 'LOG_FILE=']
        removed_count = 0
        
        for line in lines:
            if any(line.startswith(skip) for skip in skip_lines):
                removed_count += 1
                continue
            filtered_lines.append(line)
        
        if removed_count > 0:
            with open(env_path, 'w') as f:
                f.write('\n'.join(filtered_lines))
            print(f"✅ Removed {removed_count} problematic lines from .env")
        else:
            print("✅ .env file is already clean")

def create_directories():
    """Create necessary directories."""
    print("🔧 Creating necessary directories...")
    
    directories = ["models", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}/")

def test_imports():
    """Test critical imports."""
    print("🔧 Testing critical imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError:
        print("❌ FastAPI not installed. Run: pip install -r requirements.txt")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
    except ImportError:
        print("❌ SQLAlchemy not installed. Run: pip install -r requirements.txt")
        return False
    
    try:
        from app.config import settings
        print("✅ Settings imported successfully")
        return True
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False

def main():
    """Run all fixes."""
    print("🔧 GENISYS-WEB Setup Fix")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Error: Not in GENISYS-WEB directory")
        print("   Please run this script from the GENISYS-WEB directory")
        return False
    
    # Run fixes
    fix_env_file()
    create_directories()
    
    # Test the fixes
    if test_imports():
        print("\n🎉 Setup fixes completed successfully!")
        print("\n📋 Next steps:")
        print("1. Ensure PostgreSQL is running")
        print("2. Update DATABASE_URL in .env if needed")
        print("3. Run: python test_setup.py")
        print("4. Run: python -m app.main")
        return True
    else:
        print("\n❌ Some issues remain. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
