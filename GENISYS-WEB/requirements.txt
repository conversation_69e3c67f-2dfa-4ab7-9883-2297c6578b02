# GENISYS-WEB Requirements
# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.0

# Data processing and analysis
pandas==2.1.4
numpy==1.24.4
plotly==5.17.0
scipy==1.11.4

# Machine learning (for model analysis)
scikit-learn==1.3.2
lightgbm==4.1.0

# API and validation
pydantic==2.5.0
pydantic-settings==2.1.0
httpx==0.25.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Background tasks and scheduling
celery==5.3.4
redis==5.0.1

# File handling and utilities
aiofiles==23.2.1
python-magic==0.4.27

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
