#!/usr/bin/env python
"""
GENISYS-WEB Server Startup Script

Handles database initialization and starts the FastAPI server.
"""

import sys
import subprocess
from pathlib import Path

def check_database_connection():
    """Check if database is accessible."""
    print("🔍 Checking database connection...")
    
    try:
        from app.config import settings
        from sqlalchemy import create_engine, text
        
        engine = create_engine(settings.DATABASE_URL)
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        print("✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        print("💡 Make sure PostgreSQL is running and database exists")
        return False

def create_database_tables():
    """Create database tables if they don't exist."""
    print("🔧 Creating database tables...")
    
    try:
        from app.database import engine, Base
        from app.models import Worker, Experiment, ExperimentResult, ModelRegistry
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created/verified")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create tables: {e}")
        return False

def start_server():
    """Start the FastAPI server."""
    print("🚀 Starting GENISYS-WEB server...")
    
    try:
        from app.config import settings
        import uvicorn
        
        print(f"🌐 Server will be available at: http://{settings.HOST}:{settings.PORT}")
        print("📊 Dashboard: /")
        print("📚 API Docs: /api/docs")
        print("❤️  Health Check: /health")
        print("\nPress Ctrl+C to stop the server")
        
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        return False
    
    return True

def main():
    """Main startup sequence."""
    print("🚀 GENISYS-WEB Server Startup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Error: Not in GENISYS-WEB directory")
        print("   Please run this script from the GENISYS-WEB directory")
        return False
    
    # Check virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected")
        print("   Consider running: source .venv/bin/activate")
    
    # Check database connection (optional)
    db_ok = check_database_connection()
    
    if db_ok:
        # Create tables if database is available
        create_database_tables()
    else:
        print("⚠️  Continuing without database (some features may not work)")
    
    # Start the server
    return start_server()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
