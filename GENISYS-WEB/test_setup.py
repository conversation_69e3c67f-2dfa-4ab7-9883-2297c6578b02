#!/usr/bin/env python
"""
GENISYS-WEB Setup Test Script

Quick test to verify that the web server can start and basic functionality works.
"""

import sys
import subprocess
import time
import requests
from pathlib import Path

def test_imports():
    """Test that all required packages can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import pydantic
        print("✅ Core packages imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False

def test_database_models():
    """Test that database models can be imported."""
    print("🧪 Testing database models...")
    
    try:
        from app.models import Worker, Experiment, ExperimentResult
        print("✅ Database models imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Model import error: {e}")
        return False

def test_api_routes():
    """Test that API routes can be imported."""
    print("🧪 Testing API routes...")
    
    try:
        from app.routers import workers, experiments, results
        print("✅ API routes imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Route import error: {e}")
        return False

def test_app_startup():
    """Test that the FastAPI app can be created."""
    print("🧪 Testing FastAPI app creation...")
    
    try:
        from app.main import app
        print("✅ FastAPI app created successfully")
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def test_server_startup():
    """Test that the server can start (quick test)."""
    print("🧪 Testing server startup...")
    
    try:
        # Start server in background
        process = subprocess.Popen([
            sys.executable, "-m", "app.main"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for startup
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Server started successfully")
            
            # Try to make a request
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Health check endpoint responding")
                else:
                    print(f"⚠️  Health check returned status: {response.status_code}")
            except requests.RequestException:
                print("⚠️  Could not connect to server (database may not be configured)")
            
            # Stop the server
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Server failed to start")
            if stderr:
                print(f"Error: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Server startup test error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 GENISYS-WEB Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_database_models,
        test_api_routes,
        test_app_startup,
        test_server_startup
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 40)
    print("📊 Test Summary")
    print("=" * 40)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ GENISYS-WEB is ready to use!")
        print("\n🚀 Next steps:")
        print("1. Configure database in .env file")
        print("2. Start server: python -m app.main")
        print("3. Visit: http://localhost:8000")
        return True
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print("\n❌ Setup needs attention!")
        print("\n🔧 Common fixes:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ required)")
        print("3. Ensure you're in the GENISYS-WEB directory")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
