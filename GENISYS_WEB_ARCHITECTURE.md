# GENISYS-WEB Architecture Plan

## 🎯 Project Structure

```
GENISYS-WEB/
├── app/
│   ├── __init__.py
│   ├── main.py                     # FastAPI application entry point
│   ├── config.py                   # Configuration management
│   ├── database.py                 # Database connection and models
│   │
│   ├── models/                     # SQLAlchemy ORM models
│   │   ├── __init__.py
│   │   ├── worker.py              # Worker registration and status
│   │   ├── experiment.py          # Experiment definitions and batches
│   │   ├── result.py              # Experiment results and metrics
│   │   └── model_registry.py      # ML model metadata and storage
│   │
│   ├── schemas/                    # Pydantic models for API
│   │   ├── __init__.py
│   │   ├── worker.py              # Worker API schemas
│   │   ├── experiment.py          # Experiment configuration schemas
│   │   ├── result.py              # Result reporting schemas
│   │   └── strategy.py            # Trading strategy schemas
│   │
│   ├── routers/                    # FastAPI route handlers
│   │   ├── __init__.py
│   │   ├── workers.py             # Worker management API
│   │   ├── experiments.py         # Experiment submission and management
│   │   ├── results.py             # Results viewing and analysis
│   │   ├── strategies.py          # Strategy configuration API
│   │   └── models.py              # Model repository API
│   │
│   ├── services/                   # Business logic
│   │   ├── __init__.py
│   │   ├── experiment_builder.py  # GUI-driven experiment generation
│   │   ├── parameter_sweep.py     # Parameter combination generator
│   │   ├── worker_manager.py      # Worker coordination and load balancing
│   │   ├── result_analyzer.py     # Statistical analysis and ranking
│   │   └── model_manager.py       # Model storage and retrieval
│   │
│   ├── templates/                  # Jinja2 HTML templates
│   │   ├── base.html              # Base template with navigation
│   │   ├── dashboard.html         # Main dashboard
│   │   ├── experiment_builder.html # Strategy configuration GUI
│   │   ├── experiment_monitor.html # Real-time experiment monitoring
│   │   ├── results_analysis.html  # Results comparison and analysis
│   │   └── worker_status.html     # Worker management interface
│   │
│   ├── static/                     # Static assets
│   │   ├── css/
│   │   │   ├── main.css           # Main stylesheet
│   │   │   └── components.css     # Component-specific styles
│   │   ├── js/
│   │   │   ├── experiment_builder.js # Interactive strategy builder
│   │   │   ├── real_time_monitor.js  # WebSocket-based monitoring
│   │   │   └── results_charts.js     # Chart.js/Plotly visualizations
│   │   └── images/
│   │
│   └── utils/                      # Utility functions
│       ├── __init__.py
│       ├── security.py            # Authentication and authorization
│       ├── websockets.py          # Real-time communication
│       └── file_manager.py        # Model file handling
│
├── alembic/                        # Database migrations
│   ├── versions/
│   ├── env.py
│   └── alembic.ini
│
├── tests/                          # Test suite
│   ├── test_api/
│   ├── test_services/
│   └── test_models/
│
├── requirements.txt                # Python dependencies
├── docker-compose.yml             # Optional containerization
├── .env.example                   # Environment variables template
└── README.md                      # Setup and deployment guide
```

## 🗄️ Database Schema Design

### Core Tables

**workers**
- id, name, hostname, ip_address
- cpu_cores, ram_gb, resource_config
- status (online/offline/busy), last_heartbeat
- capabilities (supported timeframes, max experiments)

**experiment_batches**
- id, name, description, created_by
- total_experiments, completed_experiments
- status, created_at, completed_at
- parameter_sweep_config (JSON)

**experiments**
- id, batch_id, worker_id, name
- strategy_config (JSON), resource_requirements
- status, priority, created_at, started_at, completed_at
- progress_percent, estimated_completion

**experiment_results**
- id, experiment_id, worker_id
- performance_metrics (sharpe, win_rate, profit_factor, etc.)
- trade_statistics, model_performance
- execution_time, resource_usage

**model_registry**
- id, experiment_id, model_name, model_type
- file_path, file_size, checksum
- performance_metrics, validation_scores
- created_at, is_production_ready

### Trading-Specific Tables

**strategy_templates**
- id, name, description, category
- default_parameters, parameter_ranges
- supported_timeframes, required_indicators

**timeframe_configs**
- id, name (S5, M1, H1, etc.), polars_freq
- processing_priority, memory_multiplier

**indicator_configs**
- id, name, function_name, parameter_schema
- computation_cost, memory_usage

## 🎛️ Web Interface Features

### 1. Strategy Builder GUI
- **Timeframe Selection**: Multi-select checkboxes with resource impact preview
- **Indicator Builder**: Drag-and-drop interface with parameter sliders
- **Labeling Configuration**: Visual TP/SL/Time barrier setup
- **Model Parameters**: LightGBM hyperparameter tuning interface
- **Resource Allocation**: Worker selection and resource estimation

### 2. Parameter Sweep Generator
- **Grid Search**: Define parameter ranges and step sizes
- **Random Search**: Monte Carlo parameter sampling
- **Bayesian Optimization**: Intelligent parameter exploration
- **Resource Estimation**: Total compute time and worker allocation

### 3. Real-Time Monitoring
- **Worker Status**: Live resource usage and experiment progress
- **Experiment Queue**: Priority-based job scheduling
- **Performance Metrics**: Real-time Sharpe ratio, win rate updates
- **System Health**: Memory usage, CPU load, error rates

### 4. Results Analysis
- **Performance Ranking**: Sort by Sharpe ratio, profit factor, etc.
- **Parameter Correlation**: Heatmaps showing parameter impact
- **Equity Curves**: Interactive performance visualization
- **Statistical Significance**: Confidence intervals and p-values

### 5. Model Management
- **Model Repository**: Browse and download trained models
- **Performance Tracking**: Model validation across different periods
- **Production Deployment**: Mark models as production-ready
- **Version Control**: Model versioning and rollback capabilities

## 🔄 Worker Communication Protocol

### Registration and Heartbeat
```python
POST /api/workers/register
{
    "name": "APOLLO",
    "hostname": "apollo.local",
    "ip_address": "*************",
    "resource_config": {
        "max_ram_gb": 88,
        "max_workers": 32,
        "chunk_size": 1000000
    },
    "capabilities": {
        "supported_timeframes": ["S5", "S10", "M1", "M2", "M5", "H1", "D"],
        "max_concurrent_experiments": 4
    }
}
```

### Job Polling and Execution
```python
GET /api/workers/{worker_id}/jobs
# Returns next experiment to execute

POST /api/experiments/{exp_id}/progress
{
    "progress_percent": 45.2,
    "current_stage": "Training LightGBM model",
    "estimated_completion": "2024-01-15T14:30:00Z",
    "resource_usage": {
        "ram_usage_gb": 23.4,
        "cpu_usage_percent": 78.2
    }
}
```

### Result Submission
```python
POST /api/experiments/{exp_id}/results
{
    "performance_metrics": {
        "total_return": 15.7,
        "sharpe_ratio": 1.23,
        "win_rate": 0.58,
        "profit_factor": 1.45,
        "max_drawdown": -8.2
    },
    "trade_statistics": {
        "total_trades": 1247,
        "avg_trade_duration": "2h 15m",
        "best_trade": 2.3,
        "worst_trade": -1.8
    },
    "model_info": {
        "model_file_path": "/models/exp_12345_lightgbm.pkl",
        "feature_importance": {...},
        "validation_score": 0.67
    },
    "execution_metadata": {
        "execution_time_seconds": 1847,
        "peak_memory_usage_gb": 34.2,
        "worker_id": "apollo_001"
    }
}
```

## 🚀 Deployment Strategy

### Phase 1: Core Infrastructure (Week 1)
1. Set up FastAPI application on ORION
2. Create database schema and basic models
3. Implement worker registration and heartbeat
4. Basic experiment submission API

### Phase 2: Worker Integration (Week 2)
1. Modify GENISYS-ANALYTIC for API communication
2. Implement job polling and result reporting
3. Test with single worker (APOLLO)
4. Add ORION as second worker

### Phase 3: Web Interface (Week 3-4)
1. Strategy builder GUI
2. Real-time monitoring dashboard
3. Results analysis and visualization
4. Model repository interface

### Phase 4: Advanced Features (Month 2)
1. Parameter sweep optimization
2. Bayesian hyperparameter tuning
3. Advanced statistical analysis
4. Production model deployment tools

This architecture ensures complete separation of concerns while providing the scalability and reliability needed for systematic trading research.
