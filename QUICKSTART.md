# GENISYS Quick Start Guide

Get up and running with GENISYS in 5 minutes!

## 🚀 Immediate Setup

### 1. Run the Setup Script
```bash
python setup.py
```

This will:
- Create virtual environment
- Install all dependencies  
- Create necessary directories
- Generate sample configuration

### 2. Activate Virtual Environment
```bash
# Linux/Mac
source .venv/bin/activate

# Windows
.venv\Scripts\activate
```

### 3. Test Installation
```bash
python test_installation.py
```

### 4. Copy Your Data
```bash
# Copy your EUR/USD tick data to the data directory
cp /path/to/your/EURUSD_Ticks.csv data/
```

**Data Format Required:**
```csv
Time (UTC),Ask,Bid
2023.01.01 00:00:00.000,1.07123,1.07120
2023.01.01 00:00:01.000,1.07124,1.07121
```

## 🎯 First Run

### 1. Process Your Data
```bash
python create_master_dataset.py
```
*This creates multi-timeframe dataset with technical indicators*

### 2. Validate Data Quality
```bash
python validate_dataset.py
```
*Check validation_report.txt for results*

### 3. Configure Database (Optional)
Update database settings in config files:
- `config.json`
- `config_v2.json`

### 4. Run Your First Experiment
```bash
python run_experiment.py
```

## 🔧 Quick Configuration

### Enable Dry Run Mode
For testing with smaller datasets, edit `run_experiment.py`:

```python
SETTINGS = {
    "CONFIG_FILE": "config.json",
    "DRY_RUN": True,        # Enable dry run
    "DRY_RUN_BARS": 50000   # Use 50k bars only
}
```

### Switch Experiments
Change the config file to run different experiments:

```python
SETTINGS = {
    "CONFIG_FILE": "config_v2.json",  # Switch to v2 config
    "DRY_RUN": False,
    "DRY_RUN_BARS": 100000
}
```

## 📊 Understanding Results

### Experiment Tracking
- Results are logged to PostgreSQL (if configured)
- Key metrics: Sharpe ratio, win rate, profit factor
- All parameters are stored for reproducibility

### Performance Metrics
- **Sharpe Ratio**: Risk-adjusted returns
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss
- **Total Trades**: Number of trades executed

## 🛠️ Troubleshooting

### Memory Issues
```bash
# Enable dry run mode
# Reduce DRY_RUN_BARS to 10000 or less
# Close other applications
```

### Import Errors
```bash
# Ensure virtual environment is activated
source .venv/bin/activate
pip install -r requirements.txt
```

### Data Issues
```bash
# Check data format matches requirements
# Ensure no missing values in price columns
# Verify timestamp format: YYYY.MM.DD HH:MM:SS.fff
```

### Database Connection
```bash
# Update credentials in config files
# Ensure PostgreSQL is running
# Test connection manually
```

## 📈 Next Steps

1. **Experiment with Parameters**: Modify config files to test different strategies
2. **Add More Data**: Include additional currency pairs or timeframes  
3. **Custom Features**: Add your own technical indicators
4. **Model Tuning**: Experiment with different ML models
5. **Live Trading**: Extend to real-time data feeds

## 🔗 Key Files

- `README.md` - Complete documentation
- `config.json` - Baseline experiment configuration
- `config_v2.json` - Alternative experiment setup
- `data/README.md` - Data requirements and sources
- `validation_report.txt` - Data quality report

## 💡 Pro Tips

- Always run `validate_dataset.py` after processing new data
- Use dry run mode for development and testing
- Monitor memory usage with large datasets
- Keep backups of successful configurations
- Log all experiments for comparison

## 🆘 Need Help?

1. Check `validation_report.txt` for data issues
2. Run `python test_installation.py` to verify setup
3. Review error messages in terminal output
4. Ensure all file paths are correct
5. Verify data format matches requirements

Happy trading! 🚀
