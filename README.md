# GENISYS - Algorithmic Trading Research Platform

GENISYS is a sophisticated machine learning-based algorithmic trading research platform designed for EUR/USD forex trading. The system implements a complete pipeline from raw tick data processing to strategy backtesting with comprehensive experiment tracking.

## 🏗️ Architecture

The platform consists of three main components:

1. **Data Engineering Pipeline** (`create_master_dataset.py`) - Processes raw tick data into multi-timeframe OHLCV with technical indicators
2. **Experiment Engine** (`run_experiment.py`) - Executes ML-based trading strategies with backtesting
3. **Validation System** (`validate_dataset.py`) - Ensures data quality and generates validation reports

## 🚀 Quick Start

> **New to GENISYS?** See [QUICKSTART.md](QUICKSTART.md) for a 5-minute setup guide!

### Automated Setup (Recommended)

```bash
# Run the automated setup script
python setup.py

# Test your installation
python test_installation.py
```

### Manual Setup

1. **Prerequisites:**
   - Python 3.8+
   - PostgreSQL database (optional, for experiment tracking)
   - Raw EUR/USD tick data in CSV format

2. **Clone and setup environment:**
```bash
git clone <repository-url>
cd GENISYS
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Setup data directory:**
```bash
# Data directory is already created
# Copy your EURUSD_Ticks.csv file to the data/ directory
cp /path/to/your/EURUSD_Ticks.csv data/
```

5. **Configure database (optional):**
   - Update database credentials in `config.json` and `config_v2.json`
   - Ensure PostgreSQL is running and accessible

### Data Format Requirements

Your `EURUSD_Ticks.csv` should have the following columns:
- `Time (UTC)` - Timestamp in format: `YYYY.MM.DD HH:MM:SS.fff`
- `Ask` - Ask price (float)
- `Bid` - Bid price (float)

Example:
```csv
Time (UTC),Ask,Bid
2023.01.01 00:00:00.000,1.07123,1.07120
2023.01.01 00:00:01.000,1.07124,1.07121
```

## 📊 Usage

### 1. Create Master Dataset

Process raw tick data into multi-timeframe dataset with technical indicators:

```bash
python create_master_dataset.py
```

This will:
- Load raw tick data from `data/EURUSD_Ticks.csv`
- Create OHLCV data for 13 timeframes (5s to 1d)
- Calculate technical indicators (EMA, RSI, ADX, Bollinger Bands, ATR, MACD)
- Save processed data to `data/GENISYS_EURUSD_MASTER.parquet`

### 2. Validate Dataset

Ensure data quality before running experiments:

```bash
python validate_dataset.py
```

Generates `validation_report.txt` with:
- Dataset shape and basic statistics
- Null value analysis
- Descriptive statistics for key features

### 3. Run Experiments

Execute trading strategy experiments:

```bash
python run_experiment.py
```

The script uses the configuration specified in the `SETTINGS` section. To run different experiments:

1. Modify the `CONFIG_FILE` in `run_experiment.py`:
```python
SETTINGS = {
    "CONFIG_FILE": "config.json",  # or "config_v2.json"
    "DRY_RUN": False,
    "DRY_RUN_BARS": 100000
}
```

2. Or create new configuration files following the existing format.

## ⚙️ Configuration

### Experiment Configuration

Configuration files (`config.json`, `config_v2.json`) define:

- **Experiment metadata** - Name, notes, data source
- **Strategy parameters** - Feature set, labeling parameters, model settings
- **Trading parameters** - Confidence thresholds, risk management
- **Database connection** - PostgreSQL credentials

### Key Parameters

- `feature_set`: Technical indicators used for ML model
- `labeling_params`: 
  - `tp_multiplier`: Take profit as multiple of ATR
  - `sl_multiplier`: Stop loss as multiple of ATR  
  - `time_limit`: Maximum bars to hold position
- `confidence_threshold`: Minimum ML confidence to enter trades

## 🔬 Strategy Details

### ML Confidence Strategy

The current implementation uses:

1. **Feature Engineering**: Multi-timeframe technical indicators
2. **Labeling**: ATR-based triple barrier method (TP/SL/Time)
3. **Model**: LightGBM classifier with balanced class weights
4. **Trading**: Enter trades only when model confidence > threshold
5. **Backtesting**: VectorBT with realistic fees and slippage

### Supported Timeframes

- **Seconds**: S5, S10, S15, S30
- **Minutes**: M1, M2, M5, M10, M15, M30  
- **Hours**: H1, H4
- **Daily**: D

## 📈 Results Tracking

All experiments are logged to PostgreSQL with:

- **Experiments table**: Configuration, timestamps, notes
- **Results table**: Performance metrics (Sharpe, win rate, profit factor)

## 🛠️ Development

### Project Structure

```
GENISYS/
├── README.md                # Main documentation
├── QUICKSTART.md           # 5-minute setup guide
├── setup.py                # Automated setup script
├── test_installation.py    # Installation verification
├── .env.template           # Environment configuration template
├── .gitignore              # Git ignore rules
├── config.json             # Baseline experiment config
├── config_v2.json         # Alternative experiment config
├── create_master_dataset.py # Data processing pipeline
├── run_experiment.py       # Experiment execution engine
├── validate_dataset.py     # Data validation utilities
├── validation_report.txt   # Data quality report
├── requirements.txt        # Python dependencies
├── data/                   # Data directory (gitignored)
│   ├── README.md          # Data requirements guide
│   ├── EURUSD_Ticks.csv   # Raw tick data
│   └── GENISYS_EURUSD_MASTER.parquet # Processed dataset
└── .venv/                  # Virtual environment
```

### Memory Optimization

The system is designed for large datasets (>4GB):
- Lazy loading with Polars
- Chunked processing by timeframe
- Explicit garbage collection
- Memory-efficient indicator calculation

## 🚨 Troubleshooting

### Common Issues

1. **Memory errors**: Enable `DRY_RUN` mode for testing with smaller datasets
2. **Database connection**: Verify PostgreSQL credentials and network access
3. **Missing data**: Ensure `EURUSD_Ticks.csv` is in the `data/` directory
4. **Import errors**: Activate virtual environment and install requirements

### Performance Tips

- Use SSD storage for data files
- Allocate sufficient RAM (8GB+ recommended)
- Consider reducing timeframes if memory constrained
- Use `DRY_RUN` for development and testing

## 📝 License

[Add your license information here]

## 🤝 Contributing

[Add contribution guidelines here]
