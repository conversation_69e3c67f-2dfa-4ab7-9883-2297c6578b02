# GENISYS Resource Management Guide

This guide explains how to optimize GENISYS for different hardware configurations, specifically for your two systems.

## 🖥️ Your Hardware Configurations

### System 1: High-End (Current)
- **CPUs**: 2x Intel E5-2680 v2 (20 physical cores, 40 logical cores)
- **RAM**: 128GB total
- **Optimal Settings**: 88GB RAM usage, 32 workers, 1M row chunks
- **Performance**: Full-speed processing, no streaming required

### System 2: Mid-Range (Target)
- **CPU**: 1x Intel E5-2690 v2 (10 physical cores, 20 logical cores)  
- **RAM**: 64GB total
- **Optimal Settings**: 45GB RAM usage, 16 workers, 500K row chunks
- **Performance**: Streaming mode enabled, conservative memory usage

## 🚀 Quick Setup for Each System

### High-End System (128GB RAM)
```bash
# System auto-detects optimal settings
python manage_resources.py info
python run_experiment.py
```

**Auto-detected configuration:**
- Max RAM Usage: 88GB (70% of 128GB)
- Workers: 32 (80% of 40 logical cores)
- Chunk Size: 1,000,000 rows
- Streaming: Disabled (plenty of RAM)

### Mid-Range System (64GB RAM)
```bash
# Set up optimized configuration for 64GB system
python manage_resources.py profile mid_range_64gb

# Create resource-aware experiment configs
python manage_resources.py create-configs

# Run with optimized settings
python run_experiment.py
```

**Optimized configuration:**
- Max RAM Usage: 45GB (70% of 64GB)
- Workers: 16 (80% of 20 logical cores)
- Chunk Size: 500,000 rows
- Streaming: Enabled (memory conservation)

## ⚙️ Resource Management Commands

### Check System Resources
```bash
# Display detailed system information
python manage_resources.py info

# Run performance benchmark
python manage_resources.py benchmark

# Analyze dataset memory requirements
python manage_resources.py optimize data/GENISYS_EURUSD_MASTER.parquet
```

### Apply Hardware Profiles
```bash
# Available profiles:
python manage_resources.py profile high_end_128gb    # Your current system
python manage_resources.py profile mid_range_64gb    # Your target system
python manage_resources.py profile low_end_32gb      # Standard workstation
python manage_resources.py profile laptop_16gb       # Development laptop
```

### Create Optimized Configs
```bash
# Generate resource-aware experiment configurations
python manage_resources.py create-configs

# This creates:
# - config_resource_aware.json
# - config_v2_resource_aware.json
```

## 📊 Performance Comparison

| Metric | High-End (128GB) | Mid-Range (64GB) | Difference |
|--------|------------------|------------------|------------|
| RAM Usage | 88GB | 45GB | -49% |
| CPU Workers | 32 | 16 | -50% |
| Chunk Size | 1M rows | 500K rows | -50% |
| Streaming | Disabled | Enabled | Memory conservation |
| Processing Speed | 100% | ~70% | Estimated |
| Memory Safety | High | Very High | More conservative |

## 🔧 Manual Configuration

If you need custom settings, copy `resource_config_template.json` to `resource_config.json`:

```json
{
  "processing_limits": {
    "max_ram_gb": 45,           // Adjust for your system
    "max_workers": 16,          // CPU cores to use  
    "chunk_size": 500000,       // Rows per processing chunk
    "batch_size": 50,           // Columns per batch
    "ram_usage_percent": 0.7,   // Percentage of total RAM
    "cpu_usage_percent": 0.8    // Percentage of logical cores
  },
  "data_processing": {
    "polars_streaming": true,   // Enable for <80GB RAM systems
    "gc_frequency": 10          // Garbage collection frequency
  }
}
```

## 🚨 Memory Safety Features

### Automatic Monitoring
- **Real-time tracking**: Monitors memory usage during processing
- **Warning thresholds**: Alerts at 85% memory usage
- **Critical thresholds**: Emergency cleanup at 95% usage
- **Auto garbage collection**: Triggered at 80% usage

### Conservative Defaults
- **RAM headroom**: Uses only 60-80% of total RAM
- **CPU headroom**: Uses only 70-90% of logical cores
- **System stability**: Leaves resources for OS and other processes

### Streaming Mode
- **Automatic activation**: Enabled when RAM < 80GB
- **Lazy loading**: Processes data in chunks without loading full dataset
- **Memory efficiency**: Handles datasets larger than available RAM

## 🎯 Optimization Tips

### For 128GB System (High-End)
- Use full performance mode
- Disable streaming for speed
- Increase chunk sizes for efficiency
- Monitor for memory leaks in long runs

### For 64GB System (Mid-Range)
- Enable streaming mode
- Use conservative chunk sizes
- Increase garbage collection frequency
- Monitor memory usage closely

### General Best Practices
- **Test first**: Always run with `DRY_RUN=True` on new hardware
- **Monitor memory**: Watch for memory usage patterns
- **Adjust gradually**: Make incremental changes to settings
- **Benchmark**: Use `manage_resources.py benchmark` to test performance

## 🔍 Troubleshooting

### Memory Issues
```bash
# Check current memory usage
python manage_resources.py info

# Reduce memory usage
python manage_resources.py profile laptop_16gb  # Most conservative
```

### Performance Issues
```bash
# Run benchmark to identify bottlenecks
python manage_resources.py benchmark

# Optimize for your specific dataset
python manage_resources.py optimize data/GENISYS_EURUSD_MASTER.parquet
```

### Configuration Issues
```bash
# Reset to auto-detected settings
rm resource_config.json
python manage_resources.py info
```

## 📈 Expected Performance

### Data Processing (create_master_dataset.py)
- **128GB system**: ~15-20 minutes for full dataset
- **64GB system**: ~25-35 minutes for full dataset (streaming overhead)

### ML Training (run_experiment.py)
- **128GB system**: ~5-10 minutes per experiment
- **64GB system**: ~8-15 minutes per experiment

### Memory Usage Patterns
- **Peak usage**: During indicator calculation phase
- **Steady state**: During ML training and backtesting
- **Cleanup**: Automatic garbage collection between timeframes

The resource management system ensures your project runs optimally on both systems while maintaining stability and preventing memory-related crashes.
