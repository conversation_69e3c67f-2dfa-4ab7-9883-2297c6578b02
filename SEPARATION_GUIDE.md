# GENISYS Project Separation Guide

This guide explains how to separate the GENISYS project into two distinct, standalone applications:

## 🏗️ **Final Architecture**

```
📁 GENISYS-WEB/              (Standalone FastAPI Server)
├── FastAPI web application
├── Database models and API
├── Web interface and dashboard
├── Experiment coordination
└── Results visualization

📁 GENISYS-ANALYTIC/         (Standalone Worker Client)
├── Core trading engine
├── Resource management
├── Worker daemon
├── API client
└── Model training
```

## 🚀 **Step-by-Step Separation Process**

### **Step 1: Move GENISYS-WEB Out of Current Project**

The GENISYS-WEB directory has been created inside your current GENISYS project. Move it to be a sibling directory:

```bash
# From your current GENISYS directory
cd ..
mv GENISYS/GENISYS-WEB ./
```

**Result:**
```
📁 Documents/GENISYS/
├── GENISYS/              # Your current analytics project
└── GENISYS-WEB/          # New standalone web server
```

### **Step 2: Rename Current Project (Optional)**

For clarity, rename your current project:

```bash
mv GENISYS GENISYS-ANALYTIC
```

**Final structure:**
```
📁 Documents/GENISYS/
├── GENISYS-ANALYTIC/     # Trading engine and worker
└── GENISYS-WEB/          # Web server and dashboard
```

### **Step 3: Setup GENISYS-WEB (Standalone)**

```bash
cd GENISYS-WEB

# Create virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL settings

# Test the web server
python -m app.main
```

### **Step 4: Update GENISYS-ANALYTIC for API Communication**

The worker daemon and experiment runner are already created in your GENISYS-ANALYTIC project. Install additional dependencies:

```bash
cd ../GENISYS-ANALYTIC
source .venv/bin/activate

# Install worker communication dependencies
pip install httpx>=0.25.0

# Test worker registration
python worker_daemon.py --server-url http://localhost:8000 --worker-name TEST
```

## 🔧 **Configuration Updates**

### **GENISYS-WEB Configuration**

Edit `GENISYS-WEB/.env`:

```env
# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true

# Database Configuration (update for your PostgreSQL)
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Security
SECRET_KEY=your-secure-key-here

# File Storage
MODEL_STORAGE_PATH=./models
```

### **GENISYS-ANALYTIC Configuration**

Your existing resource management will automatically detect system capabilities. No changes needed to existing configs.

## 📋 **Database Setup**

Create a new database for GENISYS-WEB (separate from your existing GENISYS database):

```sql
-- Connect to your PostgreSQL instance
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;
```

## 🚀 **Deployment Workflow**

### **On ORION (64GB Server):**

1. **Setup GENISYS-WEB:**
```bash
cd GENISYS-WEB
source .venv/bin/activate
python -m app.main
# Web interface available at http://orion:8000
```

2. **Setup ORION Worker:**
```bash
cd ../GENISYS-ANALYTIC
source .venv/bin/activate
python worker_daemon.py --server-url http://localhost:8000 --worker-name ORION
```

### **On APOLLO (128GB Workstation):**

1. **Copy GENISYS-ANALYTIC:**
```bash
# Copy only the analytics project
scp -r GENISYS-ANALYTIC/ apollo:/path/to/GENISYS-ANALYTIC/
```

2. **Setup APOLLO Worker:**
```bash
# On APOLLO
cd GENISYS-ANALYTIC
source .venv/bin/activate
python worker_daemon.py --server-url http://orion:8000 --worker-name APOLLO
```

## ✅ **Benefits of This Architecture**

### **1. Clean Separation**
- **GENISYS-WEB**: Pure web server, no trading dependencies
- **GENISYS-ANALYTIC**: Pure computation, no web dependencies
- Clear boundaries and responsibilities

### **2. Independent Development**
- Work on web interface without affecting analytics
- Update trading algorithms without touching web code
- Separate testing and deployment cycles

### **3. Scalable Deployment**
- Deploy web server once on ORION
- Deploy analytics workers on any number of machines
- Easy to add/remove worker machines

### **4. Version Control**
- Separate git repositories if desired
- Independent release cycles
- Clear ownership of components

### **5. Resource Optimization**
- Web server uses minimal resources
- Analytics workers use full system capabilities
- No resource conflicts between components

## 🔄 **Communication Protocol**

The two applications communicate via REST API:

**Worker Registration:**
```
GENISYS-ANALYTIC → POST /api/workers/register → GENISYS-WEB
```

**Job Polling:**
```
GENISYS-ANALYTIC → GET /api/workers/{id}/jobs → GENISYS-WEB
```

**Progress Updates:**
```
GENISYS-ANALYTIC → POST /api/experiments/{id}/progress → GENISYS-WEB
```

**Result Submission:**
```
GENISYS-ANALYTIC → POST /api/experiments/{id}/results → GENISYS-WEB
```

## 🛠️ **Development Workflow**

### **Web Interface Development:**
```bash
cd GENISYS-WEB
source .venv/bin/activate
# Work on FastAPI, HTML templates, JavaScript
python -m app.main  # Test web interface
```

### **Analytics Development:**
```bash
cd GENISYS-ANALYTIC
source .venv/bin/activate
# Work on trading strategies, ML models, backtesting
python run_experiment.py  # Test analytics directly
```

### **Integration Testing:**
```bash
# Terminal 1: Start web server
cd GENISYS-WEB && python -m app.main

# Terminal 2: Start worker
cd GENISYS-ANALYTIC && python worker_daemon.py --server-url http://localhost:8000

# Terminal 3: Submit experiments via web interface
curl -X POST http://localhost:8000/api/experiments/submit
```

## 📁 **File Organization**

### **GENISYS-WEB (Web Server):**
```
GENISYS-WEB/
├── app/                  # FastAPI application
├── models/               # Trained model storage
├── logs/                 # Web server logs
├── requirements.txt      # Web-specific dependencies
└── .env                  # Web server configuration
```

### **GENISYS-ANALYTIC (Worker):**
```
GENISYS-ANALYTIC/
├── run_experiment.py     # Core trading engine
├── worker_daemon.py      # API communication
├── resource_manager.py   # Hardware optimization
├── data/                 # Trading data
├── models/               # Local model cache
└── requirements.txt      # Analytics dependencies
```

## 🎯 **Next Steps**

1. **Move GENISYS-WEB** out of current project directory
2. **Test web server** independently
3. **Test worker communication** between projects
4. **Deploy on ORION** (web server + worker)
5. **Deploy on APOLLO** (worker only)
6. **Submit first experiment** via web interface

This separation creates a professional, scalable architecture that's perfect for distributed trading research across multiple machines while maintaining clean code organization and independent development workflows.
