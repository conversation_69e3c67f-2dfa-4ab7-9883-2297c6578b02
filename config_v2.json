{"experiment_name": "ML_Confidence_Trader_M2_Context_v2_TightLabel", "notes": "Testing if a tighter SL (1.0) and Time Barrier (15) on the labels improves model performance.", "data": {"source_file": "data/GENISYS_EURUSD_MASTER.parquet"}, "strategy": {"type": "ml_confidence", "base_timeframe": "M2", "feature_set": ["dist_from_H1_EMA", "H1_ASK_EMA_50", "D_ASK_RSI_14", "S30_SPREAD_AVG"], "labeling_params": {"tp_multiplier": 1.5, "sl_multiplier": 1.0, "time_limit": 15}, "model_params": {"class_weight": "balanced", "random_state": 42}, "trade_params": {"confidence_threshold": 0.65}}, "database": {"host": "**********", "dbname": "GENISYS", "user": "postgres", "password": "postgres"}}