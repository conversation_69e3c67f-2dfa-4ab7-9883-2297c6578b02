#!/usr/bin/env python
"""
GENISYS-WEB Project Generator

Creates the complete FastAPI web server project structure for GENISYS trading system.
Run this script to generate the GENISYS-WEB project alongside your current GENISYS-ANALYTIC project.
"""

import os
from pathlib import Path
import json

def create_directory_structure():
    """Create the complete GENISYS-WEB directory structure."""
    
    base_dir = Path("../GENISYS-WEB")
    
    # Main directory structure
    directories = [
        "app",
        "app/models",
        "app/schemas", 
        "app/routers",
        "app/services",
        "app/templates",
        "app/static/css",
        "app/static/js",
        "app/static/images",
        "app/utils",
        "alembic/versions",
        "tests/test_api",
        "tests/test_services", 
        "tests/test_models",
        "models",  # For storing trained ML models
        "logs",
        "config"
    ]
    
    print(f"Creating GENISYS-WEB project structure in: {base_dir.absolute()}")
    
    # Create directories
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory.startswith("app/") and not directory.endswith((".css", ".js", ".images")):
            init_file = dir_path / "__init__.py"
            if not init_file.exists():
                init_file.touch()
    
    return base_dir

def create_main_app(base_dir):
    """Create the main FastAPI application."""
    
    # app/main.py
    main_content = '''"""
GENISYS-WEB: FastAPI Trading Strategy Research Platform

Main application entry point for the distributed GENISYS trading system.
Provides web interface for strategy configuration, experiment management,
and results analysis across multiple worker machines.
"""

from fastapi import FastAPI, Request
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.database import engine, Base
from app.routers import workers, experiments, results, strategies, models
from app.config import settings

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI application
app = FastAPI(
    title="GENISYS Trading Research Platform",
    description="Distributed trading strategy research and backtesting system",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Add CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Initialize templates
templates = Jinja2Templates(directory="app/templates")

# Include API routers
app.include_router(workers.router, prefix="/api/workers", tags=["workers"])
app.include_router(experiments.router, prefix="/api/experiments", tags=["experiments"])
app.include_router(results.router, prefix="/api/results", tags=["results"])
app.include_router(strategies.router, prefix="/api/strategies", tags=["strategies"])
app.include_router(models.router, prefix="/api/models", tags=["models"])

@app.get("/")
async def dashboard(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/experiment-builder")
async def experiment_builder(request: Request):
    """Strategy configuration and experiment builder page."""
    return templates.TemplateResponse("experiment_builder.html", {"request": request})

@app.get("/results")
async def results_page(request: Request):
    """Results analysis and visualization page."""
    return templates.TemplateResponse("results_analysis.html", {"request": request})

@app.get("/workers")
async def workers_page(request: Request):
    """Worker management and monitoring page."""
    return templates.TemplateResponse("worker_status.html", {"request": request})

@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "genisys-web"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
'''
    
    with open(base_dir / "app" / "main.py", "w") as f:
        f.write(main_content)

def create_config(base_dir):
    """Create configuration management."""
    
    # app/config.py
    config_content = '''"""
Configuration management for GENISYS-WEB application.
"""

from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Server configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False
    
    # Database configuration
    DATABASE_URL: str = "postgresql://genisys:genisys_password@localhost:5432/genisys_web"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # File storage
    MODEL_STORAGE_PATH: str = "./models"
    MAX_MODEL_FILE_SIZE: int = 1024 * 1024 * 1024  # 1GB
    
    # Worker communication
    WORKER_HEARTBEAT_TIMEOUT: int = 300  # 5 minutes
    MAX_CONCURRENT_EXPERIMENTS_PER_WORKER: int = 4
    
    # Experiment configuration
    DEFAULT_EXPERIMENT_TIMEOUT: int = 86400  # 24 hours
    MAX_PARAMETER_COMBINATIONS: int = 10000
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
'''
    
    with open(base_dir / "app" / "config.py", "w") as f:
        f.write(config_content)

def create_database_models(base_dir):
    """Create database connection and ORM models."""
    
    # app/database.py
    database_content = '''"""
Database connection and session management.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# Create database engine
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for ORM models
Base = declarative_base()

def get_db():
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
'''
    
    with open(base_dir / "app" / "database.py", "w") as f:
        f.write(database_content)

def create_requirements(base_dir):
    """Create requirements.txt file."""
    
    requirements_content = '''# GENISYS-WEB Requirements
# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.0

# Data processing and analysis
pandas==2.1.4
numpy==1.24.4
plotly==5.17.0
scipy==1.11.4

# Machine learning (for model analysis)
scikit-learn==1.3.2
lightgbm==4.1.0

# API and validation
pydantic==2.5.0
pydantic-settings==2.1.0
httpx==0.25.2

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Background tasks and scheduling
celery==5.3.4
redis==5.0.1

# File handling and utilities
aiofiles==23.2.1
python-magic==0.4.27

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
'''
    
    with open(base_dir / "requirements.txt", "w") as f:
        f.write(requirements_content)

def create_env_template(base_dir):
    """Create environment variables template."""
    
    env_content = '''# GENISYS-WEB Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Database Configuration (Update for your PostgreSQL container)
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Security (Generate secure keys for production)
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
MODEL_STORAGE_PATH=./models
MAX_MODEL_FILE_SIZE=1073741824

# Worker Configuration
WORKER_HEARTBEAT_TIMEOUT=300
MAX_CONCURRENT_EXPERIMENTS_PER_WORKER=4

# Experiment Configuration
DEFAULT_EXPERIMENT_TIMEOUT=86400
MAX_PARAMETER_COMBINATIONS=10000

# Redis Configuration (for background tasks)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/genisys-web.log
'''
    
    with open(base_dir / ".env.example", "w") as f:
        f.write(env_content)

def create_docker_compose(base_dir):
    """Create Docker Compose configuration for development."""
    
    docker_compose_content = '''version: '3.8'

services:
  genisys-web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***************************************************/genisys_web
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:17
    environment:
      POSTGRES_DB: genisys_web
      POSTGRES_USER: genisys
      POSTGRES_PASSWORD: genisys_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
'''
    
    with open(base_dir / "docker-compose.yml", "w") as f:
        f.write(docker_compose_content)

def create_readme(base_dir):
    """Create comprehensive README."""
    
    readme_content = '''# GENISYS-WEB: Trading Strategy Research Platform

FastAPI-based web interface for the distributed GENISYS trading system. Provides strategy configuration, experiment management, and results analysis across multiple worker machines.

## 🏗️ Architecture

- **GENISYS-WEB**: FastAPI server for web interface and API (runs on ORION)
- **GENISYS-ANALYTIC**: Worker instances for strategy execution (runs on ORION + APOLLO)
- **PostgreSQL**: Centralized database for experiments and results
- **Redis**: Background task queue and caching

## 🚀 Quick Start

### 1. Setup on ORION (64GB Server)

```bash
# Clone and setup
git clone <repository-url>
cd GENISYS-WEB

# Create virtual environment
python -m venv .venv
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL connection details

# Initialize database
alembic upgrade head

# Start the web server
python -m app.main
```

### 2. Setup Workers

On each machine (ORION and APOLLO), run the GENISYS-ANALYTIC worker:

```bash
cd GENISYS-ANALYTIC
python worker_daemon.py --server-url http://orion:8000
```

### 3. Access Web Interface

Open your browser to: `http://orion:8000`

## 📊 Features

### Strategy Builder
- Visual timeframe selection
- Drag-and-drop indicator configuration
- Parameter range definition
- Resource requirement estimation

### Experiment Management
- Batch experiment submission
- Parameter sweep generation
- Worker load balancing
- Real-time progress monitoring

### Results Analysis
- Performance ranking and comparison
- Statistical significance testing
- Interactive equity curves
- Parameter correlation analysis

### Model Repository
- Trained model storage and versioning
- Performance tracking across periods
- Production deployment marking
- Model file management

## 🔧 Configuration

### Database Setup

Create a PostgreSQL database for GENISYS-WEB:

```sql
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;
```

### Worker Configuration

Each GENISYS-ANALYTIC worker automatically detects its resources and registers with the web server. Manual configuration available in `resource_config.json`.

## 🚀 Deployment

### Development (Native)
```bash
python -m app.main
```

### Production (Docker)
```bash
docker-compose up -d
```

## 📁 Project Structure

```
GENISYS-WEB/
├── app/                    # FastAPI application
│   ├── models/            # Database ORM models
│   ├── schemas/           # Pydantic API schemas
│   ├── routers/           # API route handlers
│   ├── services/          # Business logic
│   ├── templates/         # HTML templates
│   └── static/            # CSS, JS, images
├── models/                # Trained ML model storage
├── logs/                  # Application logs
└── tests/                 # Test suite
```

## 🔗 API Documentation

Once running, visit:
- Swagger UI: `http://orion:8000/api/docs`
- ReDoc: `http://orion:8000/api/redoc`

## 🤝 Integration with GENISYS-ANALYTIC

The web server communicates with worker instances via REST API:
- Worker registration and heartbeat
- Experiment job distribution
- Real-time progress updates
- Result collection and storage
- Model file transfer

For more details, see `GENISYS_WEB_ARCHITECTURE.md`.
'''
    
    with open(base_dir / "README.md", "w") as f:
        f.write(readme_content)

def main():
    """Generate the complete GENISYS-WEB project."""
    print("🚀 Creating GENISYS-WEB Project Structure")
    print("=" * 50)
    
    # Create directory structure
    base_dir = create_directory_structure()
    print("✅ Created directory structure")
    
    # Create core application files
    create_main_app(base_dir)
    print("✅ Created main FastAPI application")
    
    create_config(base_dir)
    print("✅ Created configuration management")
    
    create_database_models(base_dir)
    print("✅ Created database connection")
    
    create_requirements(base_dir)
    print("✅ Created requirements.txt")
    
    create_env_template(base_dir)
    print("✅ Created environment template")
    
    create_docker_compose(base_dir)
    print("✅ Created Docker Compose configuration")
    
    create_readme(base_dir)
    print("✅ Created README documentation")
    
    print("\n🎉 GENISYS-WEB project created successfully!")
    print(f"📁 Location: {base_dir.absolute()}")
    print("\n📋 Next Steps:")
    print("1. cd ../GENISYS-WEB")
    print("2. python -m venv .venv && source .venv/bin/activate")
    print("3. pip install -r requirements.txt")
    print("4. cp .env.example .env && edit .env")
    print("5. python -m app.main")
    print("\n🌐 Then visit: http://orion:8000")

if __name__ == "__main__":
    main()
'''
