#!/usr/bin/env python
#
# GENISYS Project - Master Data Engineering Pipeline (V2 - Memory Optimized)
#
# This version is designed to handle massive datasets by processing indicators
# in chunks before the final merge, keeping peak memory usage low.
#

import pandas as pd
import polars as pl
import pandas_ta as ta
import warnings
from pathlib import Path
import time
import gc # Python's Garbage Collector
from resource_manager import ResourceManager

# --- Configuration ---
DATA_DIR = Path("data")
TICK_DATA_FILE = DATA_DIR / "EURUSD_Ticks.csv"
OUTPUT_FILE = DATA_DIR / "GENISYS_EURUSD_MASTER.parquet"
TIMEZONES = {
    'S5': '5s', 'S10': '10s', 'S15': '15s', 'S30': '30s',
    'M1': '1m', 'M2': '2m', 'M5': '5m', 'M10': '10m', 'M15': '15m', 'M30': '30m',
    'H1': '1h', 'H4': '4h', 'D': '1d'
}
TIMESTAMP_FORMAT = "%Y.%m.%d %H:%M:%S%.f"

def create_master_dataset():
    start_time = time.time()
    print("--- GENISYS: Master Data Pipeline V3 (Resource Aware) Initialized ---")

    # Initialize resource manager
    rm = ResourceManager()
    rm.print_system_info()

    # Get resource-aware configuration
    processing_config = rm.get_processing_config()
    data_config = rm.get_data_processing_config()

    print(f"\n🔧 Resource-Aware Settings:")
    print(f"   Max RAM Usage: {processing_config['max_ram_gb']} GB")
    print(f"   Batch Size: {data_config['indicator_batch_size']} columns")
    print(f"   Streaming Mode: {'Enabled' if data_config['polars_streaming'] else 'Disabled'}")
    print(f"   GC Frequency: Every {data_config['gc_frequency']} operations")

    # === Step 1: Lazy Loading ===
    print(f"\n[1/5] Scanning raw tick data from: {TICK_DATA_FILE}")
    lazy_df = pl.scan_csv(TICK_DATA_FILE).with_columns(
        pl.col("Time (UTC)").str.to_datetime(format=TIMESTAMP_FORMAT),
        (pl.col("Ask") - pl.col("Bid")).alias("spread"),
        pl.lit(1, dtype=pl.UInt32).alias("tick_volume")
    )

    # === Step 2: The "Process-in-Chunks" Loop ===
    # We will resample AND calculate indicators for each timeframe individually.
    enriched_dataframes = []
    print("\n[2/5] Resampling and enriching each timeframe individually...")

    operation_count = 0
    for name, period in TIMEZONES.items():
        print(f"\n  --- Processing Timeframe: {name} ({period}) ---")
        
        # --- A: Resample to OHLCV ---
        print(f"    -> Resampling to OHLCV...")
        ohlc_df = lazy_df.group_by_dynamic(
            "Time (UTC)", every=period, closed="left"
        ).agg(
            pl.col("Ask").first().alias("ASK_OPEN"), pl.col("Ask").max().alias("ASK_HIGH"),
            pl.col("Ask").min().alias("ASK_LOW"), pl.col("Ask").last().alias("ASK_CLOSE"),
            pl.col("Bid").first().alias("BID_OPEN"), pl.col("Bid").max().alias("BID_HIGH"),
            pl.col("Bid").min().alias("BID_LOW"), pl.col("Bid").last().alias("BID_CLOSE"),
            pl.col("spread").mean().alias("SPREAD_AVG"), pl.col("spread").max().alias("SPREAD_MAX"),
            pl.col("tick_volume").sum().alias("TICK_VOLUME")
        ).collect()

        # --- B: Calculate Indicators for this timeframe ---
        print(f"    -> Calculating indicators...")
        # We only convert THIS small timeframe's data to pandas
        ohlc_pd = ohlc_df.to_pandas()
        
        for side in ["ASK", "BID"]:
            temp_df = pd.DataFrame({
                'open': ohlc_pd[f'{side}_OPEN'], 'high': ohlc_pd[f'{side}_HIGH'],
                'low': ohlc_pd[f'{side}_LOW'], 'close': ohlc_pd[f'{side}_CLOSE'],
                'volume': ohlc_pd['TICK_VOLUME'],
            })
            
            # Using a smaller, essential set of indicators to manage column count
            custom_strategy = ta.Strategy(
                name="GENISYS_Core_Suite",
                ta=[
                    {"kind": "ema", "length": 50},
                    {"kind": "rsi", "length": 14},
                    {"kind": "adx", "length": 14},
                    {"kind": "bbands", "length": 20, "std": 2.0},
                    {"kind": "atr", "length": 14},
                    {"kind": "macd", "fast": 12, "slow": 26, "signal": 9},
                ]
            )
            temp_df.ta.strategy(custom_strategy)
            
            # Drop the original OHLCV from the temp_df to avoid duplicate columns
            temp_df.drop(columns=['open', 'high', 'low', 'close', 'volume'], inplace=True)
            # Rename indicator columns
            temp_df.columns = [f"{side}_{col}" for col in temp_df.columns]
            
            # Join indicators back to the pandas OHLC DataFrame
            ohlc_pd = ohlc_pd.join(temp_df)

        # Convert the enriched pandas DataFrame back to Polars and prefix all columns
        enriched_tf_df = pl.from_pandas(ohlc_pd)
        enriched_tf_df = enriched_tf_df.rename({col: f"{name}_{col}" for col in enriched_tf_df.columns if col != "Time (UTC)"})
        
        enriched_dataframes.append(enriched_tf_df)

        # Resource-aware memory management
        operation_count += 1

        # Check memory usage and trigger GC if needed
        if rm.should_trigger_gc() or operation_count % data_config['gc_frequency'] == 0:
            usage_percent, status = rm.check_memory_usage()
            print(f"    -> Memory usage: {usage_percent:.1f}% ({status}) - Running GC...")

        # Explicitly clean up memory
        del ohlc_df, ohlc_pd, temp_df, enriched_tf_df
        gc.collect()

        # Critical memory check
        usage_percent, status = rm.check_memory_usage()
        if status == "CRITICAL":
            print(f"    -> ⚠️  CRITICAL MEMORY USAGE: {usage_percent:.1f}%")
            print(f"    -> Consider reducing data size or increasing available RAM")
            # Force aggressive cleanup
            gc.collect()
            gc.collect()  # Double GC for critical situations

    # === Step 3: Merging Enriched Timeframes ===
    print("\n[3/5] Merging all enriched timeframes into master DataFrame...")
    master_df = enriched_dataframes[0] # Start with S5
    for i in range(1, len(enriched_dataframes)):
        print(f"  -> Joining {list(TIMEZONES.keys())[i]} data...")
        master_df = master_df.join_asof(enriched_dataframes[i], on="Time (UTC)")
        
    # Clean up the list to free more memory
    del enriched_dataframes
    gc.collect()
    
    # === Step 4: Final Feature Engineering ===
    print("\n[4/5] Engineering final time-based and relational features...")
    master_df = master_df.with_columns([
        pl.col("Time (UTC)").dt.hour().alias("Time_HourOfDay"),
        pl.col("Time (UTC)").dt.weekday().alias("Time_DayOfWeek"),
        ((pl.col("M2_ASK_CLOSE") - pl.col("H1_ASK_EMA_50")) / pl.col("M2_ASK_CLOSE")).alias("M2_dist_from_H1_EMA")
    ])

    # === Step 5: Final Cleanup and Save ===
    print("\n[5/5] Cleaning and saving the final master dataset...")
    initial_rows = master_df.height
    master_df = master_df.drop_nulls()
    print(f"  -> Dropped {initial_rows - master_df.height} initial rows with null values.")

    output_path = Path(OUTPUT_FILE)
    master_df.write_parquet(output_path)
    
    end_time = time.time()
    print("\n--- GENISYS: Master Data Pipeline V2 Complete! ---")
    print(f"  -> Final dataset shape: {master_df.shape} (rows, columns)")
    print(f"  -> Successfully saved to: {output_path}")
    print(f"  -> Total time taken: {(end_time - start_time) / 60:.2f} minutes")

if __name__ == "__main__":
    warnings.simplefilter(action='ignore', category=FutureWarning)
    warnings.simplefilter(action='ignore', category=pd.errors.PerformanceWarning)
    create_master_dataset()
