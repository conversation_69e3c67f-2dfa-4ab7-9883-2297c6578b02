# Data Directory

This directory contains the data files used by the GENISYS trading platform.

## Required Files

### 1. Raw Tick Data
**File:** `EURUSD_Ticks.csv`
**Description:** Raw EUR/USD tick data for processing

**Format Requirements:**
- CSV format with header
- Columns: `Time (UTC)`, `Ask`, `Bid`
- Timestamp format: `YYYY.MM.DD HH:MM:SS.fff`
- No missing values in price columns

**Example:**
```csv
Time (UTC),Ask,Bid
2023.01.01 00:00:00.000,1.07123,1.07120
2023.01.01 00:00:01.000,1.07124,1.07121
2023.01.01 00:00:02.000,1.07125,1.07122
```

### 2. Processed Dataset (Generated)
**File:** `GENISYS_EURUSD_MASTER.parquet`
**Description:** Multi-timeframe dataset with technical indicators
**Generated by:** `create_master_dataset.py`

## Data Sources

You can obtain EUR/USD tick data from:
- **MetaTrader 5** - Export tick data using MT5 Python API
- **Dukascopy** - Historical tick data download
- **TrueFX** - Free tick data (registration required)
- **Commercial providers** - Various paid data services

## File Size Considerations

- Raw tick data files are typically 4GB+ for substantial time periods
- Processed parquet files are more compressed but still large
- These files are excluded from git via `.gitignore`
- Ensure sufficient disk space (10GB+ recommended)

## Data Quality

Before using your data:

1. **Run validation:**
   ```bash
   python validate_dataset.py
   ```

2. **Check for:**
   - Missing timestamps
   - Unrealistic price movements
   - Data gaps during market hours
   - Proper timezone (UTC)

## Memory Requirements

Processing large datasets requires:
- **Minimum:** 8GB RAM
- **Recommended:** 16GB+ RAM
- **For development:** Use `DRY_RUN` mode in scripts

## Security Note

- Never commit actual trading data to version control
- Keep data files local or in secure cloud storage
- Consider data licensing restrictions from providers

## Getting Started

1. **Copy your tick data:**
   ```bash
   cp /path/to/your/EURUSD_Ticks.csv data/
   ```

2. **Process the data:**
   ```bash
   python create_master_dataset.py
   ```

3. **Validate the results:**
   ```bash
   python validate_dataset.py
   ```

For more information, see the main README.md file.
