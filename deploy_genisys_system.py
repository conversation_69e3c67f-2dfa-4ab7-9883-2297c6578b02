#!/usr/bin/env python
"""
GENISYS System Deployment Script

Automates the setup of the complete distributed GENISYS system:
- GENISYS-WEB server on ORION
- GENISYS-ANALYTIC workers on ORION and APOLLO
"""

import subprocess
import sys
import json
import socket
from pathlib import Path
import argparse

def run_command(command, description, cwd=None):
    """Run a shell command with error handling."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, cwd=cwd, capture_output=True, text=True)
        if result.stdout:
            print(f"   {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"   {e.stderr}")
        return False

def get_machine_type():
    """Detect which machine we're running on."""
    hostname = socket.gethostname().upper()
    
    if "<PERSON><PERSON>" in hostname:
        return "orion"
    elif "APOLLO" in hostname:
        return "apollo"
    else:
        print(f"⚠️  Unknown machine: {hostname}")
        return input("Enter machine type (orion/apollo): ").lower()

def setup_genisys_web(orion_ip="localhost"):
    """Set up GENISYS-WEB server."""
    print("\n🌐 Setting up GENISYS-WEB Server")
    print("=" * 40)
    
    # Generate GENISYS-WEB project
    if not run_command("python create_genisys_web.py", "Creating GENISYS-WEB project structure"):
        return False
    
    web_dir = Path("../GENISYS-WEB")
    
    # Create virtual environment
    if not run_command("python -m venv .venv", "Creating virtual environment", cwd=web_dir):
        return False
    
    # Install dependencies
    pip_cmd = ".venv/bin/pip" if sys.platform != "win32" else ".venv\\Scripts\\pip"
    if not run_command(f"{pip_cmd} install -r requirements.txt", "Installing dependencies", cwd=web_dir):
        return False
    
    # Create environment file
    env_content = f"""# GENISYS-WEB Environment Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql://genisys:genisys_password@localhost:5432/genisys_web

# Security
SECRET_KEY=genisys-development-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Storage
MODEL_STORAGE_PATH=./models
MAX_MODEL_FILE_SIZE=1073741824

# Worker Configuration
WORKER_HEARTBEAT_TIMEOUT=300
MAX_CONCURRENT_EXPERIMENTS_PER_WORKER=4

# Experiment Configuration
DEFAULT_EXPERIMENT_TIMEOUT=86400
MAX_PARAMETER_COMBINATIONS=10000

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/genisys-web.log
"""
    
    with open(web_dir / ".env", "w") as f:
        f.write(env_content)
    
    print("✅ GENISYS-WEB setup complete")
    print(f"📁 Location: {web_dir.absolute()}")
    print(f"🌐 Will be available at: http://{orion_ip}:8000")
    
    return True

def setup_genisys_analytic():
    """Set up GENISYS-ANALYTIC worker on current machine."""
    machine_type = get_machine_type()
    
    print(f"\n🔧 Setting up GENISYS-ANALYTIC Worker ({machine_type.upper()})")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("run_experiment.py").exists():
        print("❌ Error: Not in GENISYS-ANALYTIC directory")
        print("   Please run this script from the GENISYS project root")
        return False
    
    # Install additional dependencies for worker
    worker_deps = [
        "httpx>=0.25.0",
        "asyncio-mqtt>=0.11.0"
    ]
    
    for dep in worker_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    
    # Create resource configuration for this machine
    if machine_type == "orion":
        # 64GB system configuration
        if not run_command("python manage_resources.py profile mid_range_64gb", "Configuring for 64GB system"):
            return False
    else:
        # 128GB system - use auto-detection
        if not run_command("python manage_resources.py info", "Auto-detecting system resources"):
            return False
    
    # Create resource-aware experiment configs
    if not run_command("python manage_resources.py create-configs", "Creating resource-aware configs"):
        return False
    
    print(f"✅ GENISYS-ANALYTIC worker setup complete for {machine_type.upper()}")
    
    return True

def create_database_setup_script():
    """Create SQL script for database setup."""
    sql_content = """-- GENISYS-WEB Database Setup
-- Run this on your PostgreSQL server

-- Create database and user
CREATE DATABASE genisys_web;
CREATE USER genisys WITH PASSWORD 'genisys_password';
GRANT ALL PRIVILEGES ON DATABASE genisys_web TO genisys;

-- Connect to the new database
\\c genisys_web;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO genisys;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO genisys;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO genisys;

-- Create initial tables (these will also be created by FastAPI)
CREATE TABLE IF NOT EXISTS workers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    hostname VARCHAR(255),
    ip_address VARCHAR(45),
    resource_config JSONB,
    capabilities JSONB,
    status VARCHAR(50) DEFAULT 'offline',
    last_heartbeat TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS experiment_batches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    total_experiments INTEGER DEFAULT 0,
    completed_experiments INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS experiments (
    id SERIAL PRIMARY KEY,
    batch_id INTEGER REFERENCES experiment_batches(id),
    worker_id INTEGER REFERENCES workers(id),
    name VARCHAR(255) NOT NULL,
    strategy_config JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    progress_percent FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

CREATE TABLE IF NOT EXISTS experiment_results (
    id SERIAL PRIMARY KEY,
    experiment_id INTEGER REFERENCES experiments(id),
    performance_metrics JSONB,
    trade_statistics JSONB,
    model_metadata JSONB,
    execution_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS model_registry (
    id SERIAL PRIMARY KEY,
    experiment_id INTEGER REFERENCES experiments(id),
    model_name VARCHAR(255) NOT NULL,
    model_type VARCHAR(100),
    file_path VARCHAR(500),
    file_size_bytes BIGINT,
    performance_metrics JSONB,
    is_production_ready BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_workers_status ON workers(status);
CREATE INDEX idx_experiments_status ON experiments(status);
CREATE INDEX idx_experiments_worker ON experiments(worker_id);
CREATE INDEX idx_results_experiment ON experiment_results(experiment_id);
CREATE INDEX idx_models_experiment ON model_registry(experiment_id);

COMMIT;
"""
    
    with open("setup_database.sql", "w") as f:
        f.write(sql_content)
    
    print("✅ Created database setup script: setup_database.sql")

def create_startup_scripts():
    """Create startup scripts for different components."""
    
    # GENISYS-WEB startup script
    web_startup = """#!/bin/bash
# GENISYS-WEB Startup Script

cd "$(dirname "$0")"
source .venv/bin/activate

echo "🌐 Starting GENISYS-WEB Server..."
python -m app.main
"""
    
    with open("../GENISYS-WEB/start_web_server.sh", "w") as f:
        f.write(web_startup)
    
    # ORION worker startup script
    orion_worker = """#!/bin/bash
# GENISYS-ANALYTIC Worker Startup Script for ORION

cd "$(dirname "$0")"
source .venv/bin/activate

echo "🔧 Starting GENISYS-ANALYTIC Worker on ORION..."
python worker_daemon.py --server-url http://localhost:8000 --worker-name ORION
"""
    
    with open("start_worker_orion.sh", "w") as f:
        f.write(orion_worker)
    
    # APOLLO worker startup script
    apollo_worker = """#!/bin/bash
# GENISYS-ANALYTIC Worker Startup Script for APOLLO

cd "$(dirname "$0")"
source .venv/bin/activate

echo "🔧 Starting GENISYS-ANALYTIC Worker on APOLLO..."
# Replace 'orion' with actual IP address of ORION machine
python worker_daemon.py --server-url http://orion:8000 --worker-name APOLLO
"""
    
    with open("start_worker_apollo.sh", "w") as f:
        f.write(apollo_worker)
    
    # Make scripts executable
    import stat
    for script in ["../GENISYS-WEB/start_web_server.sh", "start_worker_orion.sh", "start_worker_apollo.sh"]:
        if Path(script).exists():
            Path(script).chmod(stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)
    
    print("✅ Created startup scripts")

def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description="Deploy GENISYS distributed system")
    parser.add_argument("--component", choices=["web", "worker", "all"], default="all",
                       help="Component to deploy")
    parser.add_argument("--orion-ip", default="localhost",
                       help="IP address of ORION machine")
    
    args = parser.parse_args()
    
    print("🚀 GENISYS Distributed System Deployment")
    print("=" * 50)
    
    machine_type = get_machine_type()
    print(f"📍 Detected machine: {machine_type.upper()}")
    
    success = True
    
    if args.component in ["web", "all"] and machine_type == "orion":
        success &= setup_genisys_web(args.orion_ip)
    
    if args.component in ["worker", "all"]:
        success &= setup_genisys_analytic()
    
    # Create additional setup files
    create_database_setup_script()
    create_startup_scripts()
    
    if success:
        print("\n🎉 Deployment completed successfully!")
        print("\n📋 Next Steps:")
        
        if machine_type == "orion":
            print("\n🗄️  Database Setup (on ORION):")
            print("   1. Ensure PostgreSQL is running")
            print("   2. Run: psql -U postgres -f setup_database.sql")
            
            print("\n🌐 Start GENISYS-WEB Server (on ORION):")
            print("   cd ../GENISYS-WEB")
            print("   ./start_web_server.sh")
            print("   # Or manually: source .venv/bin/activate && python -m app.main")
            
            print("\n🔧 Start ORION Worker:")
            print("   ./start_worker_orion.sh")
            print("   # Or manually: python worker_daemon.py --server-url http://localhost:8000")
        
        if machine_type == "apollo":
            print("\n🔧 Start APOLLO Worker:")
            print("   ./start_worker_apollo.sh")
            print(f"   # Or manually: python worker_daemon.py --server-url http://{args.orion_ip}:8000")
        
        print(f"\n🌐 Access Web Interface: http://{args.orion_ip}:8000")
        print("\n📚 Documentation:")
        print("   - GENISYS_WEB_ARCHITECTURE.md")
        print("   - RESOURCE_MANAGEMENT.md")
        print("   - README.md")
        
    else:
        print("\n❌ Deployment failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
