#!/usr/bin/env python
"""
GENISYS-ANALYTIC Experiment Runner

Executes trading strategy experiments received from GENISYS-WEB server.
Integrates with existing GENISYS codebase while providing API-driven execution.
"""

import asyncio
import json
import time
import tempfile
import pickle
import joblib
from pathlib import Path
from typing import Dict, Any, Callable, Optional
import pandas as pd

# Import existing GENISYS modules
from run_experiment import run_ml_confidence_strategy, get_db_connection, create_db_tables, log_experiment_to_db
from resource_manager import ResourceManager

class ExperimentRunner:
    """Executes experiments with progress reporting and result collection."""
    
    def __init__(self, resource_manager: ResourceManager):
        self.resource_manager = resource_manager
        self.is_experiment_running = False
        self.current_progress = 0.0
        self.current_stage = "idle"
        
    def is_running(self) -> bool:
        """Check if an experiment is currently running."""
        return self.is_experiment_running
    
    async def run_experiment(self, config: Dict[str, Any], progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Run a complete trading strategy experiment.
        
        Args:
            config: Experiment configuration (similar to config.json format)
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary containing experiment results and metadata
        """
        self.is_experiment_running = True
        self.current_progress = 0.0
        
        try:
            # Validate configuration
            self._validate_config(config)
            
            # Report initial progress
            await self._report_progress(5.0, "Loading configuration", progress_callback)
            
            # Load and validate data
            await self._report_progress(10.0, "Loading dataset", progress_callback)
            df = self._load_dataset(config)
            
            # Setup database connection (if configured)
            db_engine = None
            experiment_id = None
            if "database" in config:
                await self._report_progress(15.0, "Connecting to database", progress_callback)
                db_engine = get_db_connection(config["database"])
                if db_engine:
                    create_db_tables(db_engine)
                    experiment_id = log_experiment_to_db(db_engine, config)
            
            # Execute strategy
            await self._report_progress(20.0, "Starting strategy execution", progress_callback)
            
            strategy_type = config["strategy"]["type"]
            if strategy_type == "ml_confidence":
                results = await self._run_ml_confidence_strategy(
                    config, df, db_engine, experiment_id, progress_callback
                )
            else:
                raise ValueError(f"Unsupported strategy type: {strategy_type}")
            
            # Finalize results
            await self._report_progress(95.0, "Finalizing results", progress_callback)
            final_results = self._prepare_final_results(config, results)
            
            await self._report_progress(100.0, "Experiment completed", progress_callback)
            
            return final_results
            
        except Exception as e:
            await self._report_progress(100.0, f"Experiment failed: {str(e)}", progress_callback)
            raise
        finally:
            self.is_experiment_running = False
    
    def _validate_config(self, config: Dict[str, Any]):
        """Validate experiment configuration."""
        required_fields = ["experiment_name", "strategy", "data"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")
        
        if "type" not in config["strategy"]:
            raise ValueError("Strategy type not specified")
        
        if "source_file" not in config["data"]:
            raise ValueError("Data source file not specified")
    
    def _load_dataset(self, config: Dict[str, Any]) -> pd.DataFrame:
        """Load and prepare dataset for experiment."""
        source_file = config["data"]["source_file"]
        
        # Handle relative paths
        if not Path(source_file).is_absolute():
            source_file = Path("data") / source_file
        
        if not Path(source_file).exists():
            raise FileNotFoundError(f"Dataset not found: {source_file}")
        
        # Load dataset
        df = pd.read_parquet(source_file)
        df = df.set_index('Time (UTC)')
        
        # Apply dry run limits if specified
        if config.get("dry_run", False):
            dry_run_bars = config.get("dry_run_bars", 100000)
            df = df.head(dry_run_bars)
        
        return df
    
    async def _run_ml_confidence_strategy(
        self, 
        config: Dict[str, Any], 
        df: pd.DataFrame, 
        db_engine, 
        experiment_id: int,
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Execute ML confidence strategy with progress reporting."""
        
        # Patch the original function to report progress
        original_print = print
        
        def progress_print(*args, **kwargs):
            message = " ".join(str(arg) for arg in args)
            
            # Update progress based on message content
            if "Engineering relational features" in message:
                asyncio.create_task(self._report_progress(25.0, "Engineering features", progress_callback))
            elif "Preparing data" in message:
                asyncio.create_task(self._report_progress(35.0, "Preparing data", progress_callback))
            elif "Training LightGBM model" in message:
                asyncio.create_task(self._report_progress(50.0, "Training ML model", progress_callback))
            elif "Making probability predictions" in message:
                asyncio.create_task(self._report_progress(70.0, "Making predictions", progress_callback))
            elif "Backtesting with confidence threshold" in message:
                asyncio.create_task(self._report_progress(80.0, "Running backtest", progress_callback))
            
            # Call original print
            original_print(*args, **kwargs)
        
        # Temporarily replace print function
        import builtins
        builtins.print = progress_print
        
        try:
            # Create a temporary results collector
            results_collector = ExperimentResultsCollector()
            
            # Run the strategy (this will use the existing run_ml_confidence_strategy function)
            # We need to modify it slightly to collect results instead of just logging to DB
            strategy_results = await self._execute_ml_strategy_with_collection(
                config, df, db_engine, experiment_id, results_collector
            )
            
            return strategy_results
            
        finally:
            # Restore original print function
            builtins.print = original_print
    
    async def _execute_ml_strategy_with_collection(
        self, 
        config: Dict[str, Any], 
        df: pd.DataFrame, 
        db_engine, 
        experiment_id: int,
        results_collector
    ) -> Dict[str, Any]:
        """Execute ML strategy and collect detailed results."""
        
        # This is a modified version of run_ml_confidence_strategy that collects results
        cfg = config['strategy']
        base_tf = cfg['base_timeframe']
        feature_cols = cfg['feature_set']
        label_p = cfg['labeling_params']
        model_p = cfg['model_params'].copy()
        trade_p = cfg['trade_params']
        
        # Apply resource-aware settings
        ml_config = self.resource_manager.get_ml_config()
        model_p['n_jobs'] = ml_config['lightgbm_num_threads']
        model_p['num_threads'] = ml_config['lightgbm_num_threads']
        
        print("  -> Engineering relational features...")
        if 'dist_from_H1_EMA' in feature_cols:
            df['dist_from_H1_EMA'] = (df[f'{base_tf}_ASK_CLOSE'] - df['H1_ASK_EMA_50']) / df[f'{base_tf}_ASK_CLOSE']
        
        print(f"  -> Preparing data for {base_tf} timeframe...")
        close_price = df[f'{base_tf}_ASK_CLOSE']
        
        # Import required modules
        import pandas_ta as ta
        import lightgbm as lgb
        import vectorbt as vbt
        from numba import njit
        import numpy as np
        
        atr = ta.atr(high=df[f'{base_tf}_ASK_HIGH'], low=df[f'{base_tf}_ASK_LOW'], close=close_price, length=14)
        
        # Generate labels (using existing function)
        from run_experiment import generate_labels_nb
        labels_array = generate_labels_nb(
            close_price.values, 
            df[f'{base_tf}_ASK_HIGH'].values, 
            df[f'{base_tf}_BID_LOW'].values, 
            atr.values, 
            label_p['tp_multiplier'], 
            label_p['sl_multiplier'], 
            label_p['time_limit']
        )
        df['label'] = pd.Series(labels_array, index=df.index, dtype='int8')
        
        print("  -> Preparing feature set and splitting data...")
        full_data = pd.concat([df[feature_cols], df['label']], axis=1).dropna()
        X, y = full_data[feature_cols], full_data['label']
        train_size = int(len(X) * 0.8)
        X_train, X_test, y_train, y_test = X[:train_size], X[train_size:], y[:train_size], y[train_size:]
        
        print("  -> Training LightGBM model...")
        lgb_clf = lgb.LGBMClassifier(objective='multiclass', **model_p)
        lgb_clf.fit(X_train, y_train)
        
        # Save model if requested
        model_info = None
        if config.get("save_model", True):
            model_info = await self._save_model(lgb_clf, config, X_train.columns.tolist())
        
        print("  -> Making probability predictions...")
        y_pred_proba = lgb_clf.predict_proba(X_test)
        
        # Get confidence thresholds
        confidence_thresholds = trade_p.get('confidence_thresholds', [trade_p.get('confidence_threshold')])
        
        all_results = []
        
        for thresh in confidence_thresholds:
            if thresh is None:
                continue
                
            print(f"    -> Backtesting with confidence threshold > {thresh:.2f}...")
            
            long_entries = y_pred_proba[:, lgb_clf.classes_ == 1].flatten() > thresh
            short_entries = y_pred_proba[:, lgb_clf.classes_ == -1].flatten() > thresh
            long_entries = pd.Series(long_entries, index=X_test.index)
            short_entries = pd.Series(short_entries, index=X_test.index)
            
            if long_entries.sum() == 0 and short_entries.sum() == 0:
                print("      -> No trades generated for this threshold. Skipping.")
                continue
            
            # Run backtest
            processing_config = self.resource_manager.get_processing_config()
            pf = vbt.Portfolio.from_signals(
                close=df[f'{base_tf}_ASK_CLOSE'].loc[X_test.index],
                open=df[f'{base_tf}_ASK_OPEN'].loc[X_test.index],
                high=df[f'{base_tf}_ASK_HIGH'].loc[X_test.index],
                low=df[f'{base_tf}_BID_LOW'].loc[X_test.index],
                entries=long_entries,
                short_entries=short_entries,
                sl_stop=(atr.loc[X_test.index] * label_p['sl_multiplier']),
                tp_stop=(atr.loc[X_test.index] * label_p['tp_multiplier']),
                fees=0.0001,
                slippage=0.0001,
                freq=f"{base_tf[1:]}{base_tf[0].lower()}",
                chunked=processing_config['max_ram_gb'] < 80
            )
            
            if pf.trades.count() > 5:
                stats = pf.stats()
                result_data = {
                    'parameters': {'confidence_threshold': thresh},
                    'total_return': float(stats.get('Total Return [%]', 0.0)),
                    'sharpe_ratio': float(stats.get('Sharpe Ratio', 0.0)),
                    'win_rate': float(stats.get('Win Rate [%]', 0.0)),
                    'total_trades': int(stats.get('Total Trades', 0)),
                    'profit_factor': float(stats.get('Profit Factor', 0.0)),
                    'max_drawdown': float(stats.get('Max Drawdown [%]', 0.0)),
                    'avg_trade_duration': str(stats.get('Avg Trade Duration', 'N/A')),
                    'best_trade': float(stats.get('Best Trade [%]', 0.0)),
                    'worst_trade': float(stats.get('Worst Trade [%]', 0.0))
                }
                
                all_results.append(result_data)
                
                # Log to database if available
                if db_engine and experiment_id:
                    from run_experiment import log_result_to_db
                    log_result_to_db(db_engine, experiment_id, result_data)
                
                print(f"      -> Completed: Sharpe = {result_data['sharpe_ratio']:.2f}, Trades = {result_data['total_trades']}")
        
        # Prepare comprehensive results
        experiment_results = {
            'experiment_config': config,
            'model_info': model_info,
            'feature_importance': dict(zip(X_train.columns, lgb_clf.feature_importances_)) if hasattr(lgb_clf, 'feature_importances_') else {},
            'data_info': {
                'total_samples': len(full_data),
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': len(feature_cols)
            },
            'threshold_results': all_results,
            'execution_metadata': {
                'execution_time_seconds': time.time() - (self.current_progress / 100 * 3600),  # Rough estimate
                'worker_name': self.resource_manager.system_info.get('hostname', 'unknown'),
                'resource_config': self.resource_manager.get_processing_config()
            }
        }
        
        return experiment_results
    
    async def _save_model(self, model, config: Dict[str, Any], feature_names: list) -> Dict[str, Any]:
        """Save trained model and return metadata."""
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        
        # Generate model filename
        experiment_name = config.get("experiment_name", "unknown")
        timestamp = int(time.time())
        model_filename = f"{experiment_name}_{timestamp}_lightgbm.pkl"
        model_path = models_dir / model_filename
        
        # Save model
        with open(model_path, 'wb') as f:
            pickle.dump({
                'model': model,
                'feature_names': feature_names,
                'config': config,
                'timestamp': timestamp
            }, f)
        
        # Calculate file size
        file_size = model_path.stat().st_size
        
        model_info = {
            'model_file_path': str(model_path),
            'model_filename': model_filename,
            'file_size_bytes': file_size,
            'feature_names': feature_names,
            'model_type': 'lightgbm',
            'timestamp': timestamp
        }
        
        print(f"  -> Model saved: {model_filename} ({file_size / 1024 / 1024:.1f} MB)")
        
        return model_info
    
    def _prepare_final_results(self, config: Dict[str, Any], strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare final results for transmission to server."""
        
        # Find best performing threshold result
        best_result = None
        if strategy_results.get('threshold_results'):
            best_result = max(
                strategy_results['threshold_results'],
                key=lambda x: x.get('sharpe_ratio', -999)
            )
        
        final_results = {
            'experiment_name': config.get('experiment_name'),
            'strategy_type': config['strategy']['type'],
            'performance_summary': best_result or {},
            'all_threshold_results': strategy_results.get('threshold_results', []),
            'model_metadata': strategy_results.get('model_info', {}),
            'feature_importance': strategy_results.get('feature_importance', {}),
            'data_statistics': strategy_results.get('data_info', {}),
            'execution_metadata': strategy_results.get('execution_metadata', {}),
            'resource_usage': self.resource_manager.get_processing_config()
        }
        
        return final_results
    
    async def _report_progress(self, progress: float, stage: str, callback: Optional[Callable] = None):
        """Report progress to callback if provided."""
        self.current_progress = progress
        self.current_stage = stage
        
        if callback:
            progress_data = {
                'progress_percent': progress,
                'current_stage': stage,
                'estimated_completion': None,  # Could calculate based on progress rate
                'resource_usage': {
                    'ram_usage_gb': self.resource_manager.system_info['total_ram_gb'] * 0.3,  # Rough estimate
                    'cpu_usage_percent': 75.0  # Rough estimate
                }
            }
            
            await callback(progress_data)

class ExperimentResultsCollector:
    """Collects experiment results for transmission to server."""
    
    def __init__(self):
        self.results = []
        self.metadata = {}
    
    def add_result(self, result: Dict[str, Any]):
        """Add a result to the collection."""
        self.results.append(result)
    
    def set_metadata(self, key: str, value: Any):
        """Set metadata for the experiment."""
        self.metadata[key] = value
    
    def get_all_results(self) -> Dict[str, Any]:
        """Get all collected results."""
        return {
            'results': self.results,
            'metadata': self.metadata
        }
