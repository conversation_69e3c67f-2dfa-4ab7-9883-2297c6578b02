#!/usr/bin/env python
"""
GENISYS Resource Management Utility

Command-line tool for managing system resources and creating optimized configurations.
"""

import argparse
import json
import sys
from pathlib import Path
from resource_manager import ResourceManager, create_resource_aware_config

def show_system_info():
    """Display detailed system information."""
    rm = ResourceManager()
    rm.print_system_info()

def create_config_for_profile(profile_name: str):
    """Create a resource config based on a predefined profile."""
    template_path = Path("resource_config_template.json")
    
    if not template_path.exists():
        print("❌ resource_config_template.json not found!")
        return False
    
    with open(template_path, 'r') as f:
        template = json.load(f)
    
    if profile_name not in template["hardware_profiles"]:
        print(f"❌ Profile '{profile_name}' not found!")
        print("Available profiles:")
        for name, profile in template["hardware_profiles"].items():
            print(f"  - {name}: {profile['description']}")
        return False
    
    profile = template["hardware_profiles"][profile_name]
    
    # Create config based on profile
    config = {
        "system_tier": "custom",
        "detected_resources": template["detected_resources"],
        "processing_limits": {
            "max_ram_gb": profile["max_ram_gb"],
            "max_workers": profile["max_workers"],
            "chunk_size": profile["chunk_size"],
            "batch_size": profile["batch_size"],
            "ram_usage_percent": 0.7,
            "cpu_usage_percent": 0.8
        },
        "data_processing": {
            "polars_streaming": profile["polars_streaming"],
            "pandas_chunksize": profile["chunk_size"] // 4,
            "indicator_batch_size": profile["batch_size"],
            "gc_frequency": profile["gc_frequency"]
        },
        "ml_training": {
            "lightgbm_num_threads": profile["max_workers"],
            "vectorbt_num_workers": profile["max_workers"],
            "sklearn_n_jobs": profile["max_workers"]
        },
        "memory_monitoring": {
            "enable_monitoring": True,
            "warning_threshold_percent": 85,
            "critical_threshold_percent": 95,
            "auto_gc_threshold_percent": 80
        }
    }
    
    # Save config
    config_path = Path("resource_config.json")
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Created resource config for profile: {profile_name}")
    print(f"   Description: {profile['description']}")
    print(f"   Config saved to: {config_path}")
    
    return True

def benchmark_system():
    """Run a simple benchmark to test system performance."""
    import time
    import numpy as np
    import pandas as pd
    
    print("🧪 Running system benchmark...")
    rm = ResourceManager()
    
    # CPU benchmark
    print("\n📊 CPU Benchmark:")
    start_time = time.time()
    
    # Simple CPU-intensive task
    data = np.random.randn(1000000)
    result = np.fft.fft(data)
    cpu_time = time.time() - start_time
    
    print(f"   FFT of 1M random numbers: {cpu_time:.2f} seconds")
    
    # Memory benchmark
    print("\n💾 Memory Benchmark:")
    start_time = time.time()
    
    # Create and process a DataFrame
    df = pd.DataFrame(np.random.randn(100000, 50))
    df_processed = df.rolling(window=10).mean()
    memory_time = time.time() - start_time
    
    print(f"   Process 100K x 50 DataFrame: {memory_time:.2f} seconds")
    
    # Overall score
    config = rm.get_processing_config()
    score = (config["max_ram_gb"] * config["max_workers"]) / (cpu_time + memory_time)
    
    print(f"\n🏆 Performance Score: {score:.1f}")
    print(f"   (Higher is better, based on RAM, cores, and benchmark times)")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if score > 1000:
        print("   ✅ Excellent performance - suitable for large datasets")
    elif score > 500:
        print("   ✅ Good performance - suitable for medium datasets")
    elif score > 200:
        print("   ⚠️  Moderate performance - consider using streaming mode")
    else:
        print("   ⚠️  Limited performance - use dry run mode for testing")

def optimize_for_dataset(dataset_path: str):
    """Analyze a dataset and suggest optimal resource settings."""
    try:
        import polars as pl
        
        print(f"📊 Analyzing dataset: {dataset_path}")
        
        # Get basic info without loading full dataset
        df_info = pl.scan_parquet(dataset_path)
        
        # Try to get schema and estimated size
        try:
            schema = df_info.schema
            print(f"   Columns: {len(schema)}")
            
            # Sample a small portion to estimate memory usage
            sample = df_info.head(10000).collect()
            sample_memory_mb = sample.estimated_size() / (1024 * 1024)
            
            # Estimate full dataset memory usage
            estimated_rows = 10000000  # Rough estimate, could be improved
            estimated_memory_gb = (sample_memory_mb * estimated_rows / 10000) / 1024
            
            print(f"   Estimated memory usage: {estimated_memory_gb:.1f} GB")
            
            # Get current system resources
            rm = ResourceManager()
            config = rm.get_processing_config()
            
            print(f"\n💡 Optimization Suggestions:")
            
            if estimated_memory_gb > config["max_ram_gb"]:
                print(f"   ⚠️  Dataset larger than available RAM ({config['max_ram_gb']} GB)")
                print(f"   📝 Recommendation: Enable streaming mode")
                print(f"   📝 Recommendation: Reduce chunk_size to {config['chunk_size'] // 2}")
            else:
                print(f"   ✅ Dataset fits in available RAM")
                print(f"   📝 Recommendation: Disable streaming for better performance")
            
            if len(schema) > 200:
                print(f"   ⚠️  Large number of columns ({len(schema)})")
                print(f"   📝 Recommendation: Reduce batch_size to {min(50, config['batch_size'])}")
            
        except Exception as e:
            print(f"   ⚠️  Could not analyze dataset details: {e}")
            
    except ImportError:
        print("❌ Polars not available for dataset analysis")
    except Exception as e:
        print(f"❌ Error analyzing dataset: {e}")

def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="GENISYS Resource Management Utility",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python manage_resources.py info                    # Show system information
  python manage_resources.py profile mid_range_64gb  # Use predefined profile
  python manage_resources.py benchmark               # Run performance benchmark
  python manage_resources.py optimize data/file.parquet  # Optimize for dataset
  python manage_resources.py create-configs          # Create resource-aware configs
        """
    )
    
    parser.add_argument('command', choices=['info', 'profile', 'benchmark', 'optimize', 'create-configs'],
                       help='Command to execute')
    parser.add_argument('target', nargs='?', help='Profile name or dataset path')
    
    args = parser.parse_args()
    
    if args.command == 'info':
        show_system_info()
        
    elif args.command == 'profile':
        if not args.target:
            print("❌ Profile name required")
            print("Available profiles: high_end_128gb, mid_range_64gb, low_end_32gb, laptop_16gb")
            sys.exit(1)
        create_config_for_profile(args.target)
        
    elif args.command == 'benchmark':
        benchmark_system()
        
    elif args.command == 'optimize':
        if not args.target:
            print("❌ Dataset path required")
            sys.exit(1)
        optimize_for_dataset(args.target)
        
    elif args.command == 'create-configs':
        print("🔧 Creating resource-aware experiment configurations...")
        for config_file in ["config.json", "config_v2.json"]:
            if Path(config_file).exists():
                try:
                    output_file = create_resource_aware_config(config_file)
                    print(f"✅ Created: {output_file}")
                except Exception as e:
                    print(f"❌ Error creating config for {config_file}: {e}")
            else:
                print(f"⚠️  {config_file} not found, skipping")

if __name__ == "__main__":
    main()
