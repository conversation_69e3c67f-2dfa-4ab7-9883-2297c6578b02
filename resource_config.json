{"system_tier": "custom", "detected_resources": {"_comment": "These values are auto-detected but can be overridden", "cpu_physical_cores": 20, "cpu_logical_cores": 40, "total_ram_gb": 128.0, "available_ram_gb": 120.0, "max_cpu_freq_mhz": 3000, "platform": "posix"}, "processing_limits": {"max_ram_gb": 45, "max_workers": 16, "chunk_size": 500000, "batch_size": 50, "ram_usage_percent": 0.7, "cpu_usage_percent": 0.8}, "data_processing": {"polars_streaming": true, "pandas_chunksize": 125000, "indicator_batch_size": 50, "gc_frequency": 10}, "ml_training": {"lightgbm_num_threads": 16, "vectorbt_num_workers": 16, "sklearn_n_jobs": 16}, "memory_monitoring": {"enable_monitoring": true, "warning_threshold_percent": 85, "critical_threshold_percent": 95, "auto_gc_threshold_percent": 80}}