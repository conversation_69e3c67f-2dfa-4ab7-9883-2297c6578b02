{"_comment": "GENISYS Resource Configuration Template", "_description": "Manual override for system resource detection. Copy to 'resource_config.json' and modify as needed.", "system_tier": "custom", "detected_resources": {"_comment": "These values are auto-detected but can be overridden", "cpu_physical_cores": 20, "cpu_logical_cores": 40, "total_ram_gb": 128.0, "available_ram_gb": 120.0, "max_cpu_freq_mhz": 3000, "platform": "posix"}, "processing_limits": {"_comment": "Core processing limits - adjust these for your hardware", "max_ram_gb": 90, "max_workers": 32, "chunk_size": 1000000, "batch_size": 100, "ram_usage_percent": 0.7, "cpu_usage_percent": 0.8}, "data_processing": {"_comment": "Data processing specific settings", "polars_streaming": false, "pandas_chunksize": 250000, "indicator_batch_size": 100, "gc_frequency": 20}, "ml_training": {"_comment": "Machine learning framework settings", "lightgbm_num_threads": 32, "vectorbt_num_workers": 32, "sklearn_n_jobs": 32}, "memory_monitoring": {"_comment": "Memory monitoring and safety thresholds", "enable_monitoring": true, "warning_threshold_percent": 85, "critical_threshold_percent": 95, "auto_gc_threshold_percent": 80}, "hardware_profiles": {"_comment": "Predefined profiles for common hardware configurations", "high_end_128gb": {"description": "2x E5-2680 v2, 128GB RAM", "max_ram_gb": 90, "max_workers": 32, "chunk_size": 1000000, "batch_size": 100, "polars_streaming": false, "gc_frequency": 20}, "mid_range_64gb": {"description": "1x E5-2690 v2, 64GB RAM", "max_ram_gb": 45, "max_workers": 16, "chunk_size": 500000, "batch_size": 50, "polars_streaming": true, "gc_frequency": 10}, "low_end_32gb": {"description": "Standard workstation, 32GB RAM", "max_ram_gb": 20, "max_workers": 8, "chunk_size": 250000, "batch_size": 25, "polars_streaming": true, "gc_frequency": 5}, "laptop_16gb": {"description": "Development laptop, 16GB RAM", "max_ram_gb": 10, "max_workers": 4, "chunk_size": 100000, "batch_size": 10, "polars_streaming": true, "gc_frequency": 3}}, "optimization_tips": {"_comment": "Guidelines for optimizing settings", "ram_usage": "Use 60-80% of total RAM, leaving headroom for OS and other processes", "cpu_usage": "Use 70-90% of logical cores for CPU-intensive tasks", "chunk_size": "Larger chunks = better performance but more RAM usage", "batch_size": "Reduce if running out of memory during indicator calculation", "streaming": "Enable for datasets that don't fit in RAM", "gc_frequency": "Lower values = more frequent cleanup, less memory usage"}}