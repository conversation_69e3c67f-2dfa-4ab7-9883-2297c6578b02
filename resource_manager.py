#!/usr/bin/env python
"""
GENISYS Resource Manager

Automatically detects system resources and configures optimal processing parameters
for different hardware configurations. Provides safe defaults with manual overrides.
"""

import psutil
import os
import json
from pathlib import Path
from typing import Dict, Tuple, Optional

class ResourceManager:
    """Manages system resources and provides optimal configuration parameters."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize resource manager with optional config override."""
        self.config_file = config_file or "resource_config.json"
        self.system_info = self._detect_system_resources()
        self.config = self._load_or_create_config()
        
    def _detect_system_resources(self) -> Dict:
        """Detect available system resources."""
        # CPU Information
        cpu_count_physical = psutil.cpu_count(logical=False)
        cpu_count_logical = psutil.cpu_count(logical=True)
        
        # Memory Information (in GB)
        memory = psutil.virtual_memory()
        total_ram_gb = memory.total / (1024**3)
        available_ram_gb = memory.available / (1024**3)
        
        # CPU frequency (if available)
        try:
            cpu_freq = psutil.cpu_freq()
            max_freq = cpu_freq.max if cpu_freq else None
        except:
            max_freq = None
            
        return {
            "cpu_physical_cores": cpu_count_physical,
            "cpu_logical_cores": cpu_count_logical,
            "total_ram_gb": round(total_ram_gb, 1),
            "available_ram_gb": round(available_ram_gb, 1),
            "max_cpu_freq_mhz": max_freq,
            "platform": os.name
        }
    
    def _load_or_create_config(self) -> Dict:
        """Load existing config or create default based on system resources."""
        config_path = Path(self.config_file)
        
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                print(f"Loaded resource config from {config_path}")
                return config
            except Exception as e:
                print(f"Error loading config: {e}. Creating new config.")
        
        # Create default config based on detected resources
        config = self._create_default_config()
        self._save_config(config)
        return config
    
    def _create_default_config(self) -> Dict:
        """Create default configuration based on detected system resources."""
        total_ram = self.system_info["total_ram_gb"]
        physical_cores = self.system_info["cpu_physical_cores"]
        logical_cores = self.system_info["cpu_logical_cores"]
        
        # Determine system tier based on resources
        if total_ram >= 120 and physical_cores >= 20:
            tier = "high_end"
            ram_usage_percent = 0.7  # Use 70% of RAM
            cpu_usage_percent = 0.8  # Use 80% of cores
        elif total_ram >= 60 and physical_cores >= 10:
            tier = "mid_range"
            ram_usage_percent = 0.6  # Use 60% of RAM
            cpu_usage_percent = 0.7  # Use 70% of cores
        else:
            tier = "low_end"
            ram_usage_percent = 0.5  # Use 50% of RAM
            cpu_usage_percent = 0.6  # Use 60% of cores
        
        # Calculate safe limits
        max_ram_gb = int(total_ram * ram_usage_percent)
        max_workers = max(1, int(logical_cores * cpu_usage_percent))
        
        # Data processing parameters based on available RAM
        if max_ram_gb >= 80:
            chunk_size = 1000000  # 1M rows per chunk
            batch_size = 100      # 100 columns per batch
        elif max_ram_gb >= 40:
            chunk_size = 500000   # 500K rows per chunk
            batch_size = 50       # 50 columns per batch
        else:
            chunk_size = 250000   # 250K rows per chunk
            batch_size = 25       # 25 columns per batch
        
        config = {
            "system_tier": tier,
            "detected_resources": self.system_info,
            "processing_limits": {
                "max_ram_gb": max_ram_gb,
                "max_workers": max_workers,
                "chunk_size": chunk_size,
                "batch_size": batch_size,
                "ram_usage_percent": ram_usage_percent,
                "cpu_usage_percent": cpu_usage_percent
            },
            "data_processing": {
                "polars_streaming": max_ram_gb < 60,  # Use streaming for lower RAM
                "pandas_chunksize": chunk_size // 4,
                "indicator_batch_size": batch_size,
                "gc_frequency": 10 if max_ram_gb < 60 else 20  # More frequent GC on low RAM
            },
            "ml_training": {
                "lightgbm_num_threads": max_workers,
                "vectorbt_num_workers": max_workers,
                "sklearn_n_jobs": max_workers
            },
            "memory_monitoring": {
                "enable_monitoring": True,
                "warning_threshold_percent": 85,
                "critical_threshold_percent": 95,
                "auto_gc_threshold_percent": 80
            }
        }
        
        return config
    
    def _save_config(self, config: Dict):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"Saved resource config to {self.config_file}")
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get_processing_config(self) -> Dict:
        """Get processing configuration for current system."""
        return self.config["processing_limits"]
    
    def get_data_processing_config(self) -> Dict:
        """Get data processing specific configuration."""
        return self.config["data_processing"]
    
    def get_ml_config(self) -> Dict:
        """Get machine learning specific configuration."""
        return self.config["ml_training"]
    
    def get_memory_config(self) -> Dict:
        """Get memory monitoring configuration."""
        return self.config["memory_monitoring"]
    
    def check_memory_usage(self) -> Tuple[float, str]:
        """Check current memory usage and return percentage and status."""
        memory = psutil.virtual_memory()
        usage_percent = memory.percent
        
        thresholds = self.get_memory_config()
        
        if usage_percent >= thresholds["critical_threshold_percent"]:
            status = "CRITICAL"
        elif usage_percent >= thresholds["warning_threshold_percent"]:
            status = "WARNING"
        elif usage_percent >= thresholds["auto_gc_threshold_percent"]:
            status = "HIGH"
        else:
            status = "OK"
            
        return usage_percent, status
    
    def should_trigger_gc(self) -> bool:
        """Check if garbage collection should be triggered."""
        usage_percent, _ = self.check_memory_usage()
        return usage_percent >= self.get_memory_config()["auto_gc_threshold_percent"]
    
    def print_system_info(self):
        """Print detailed system information and configuration."""
        print("\n" + "="*60)
        print("GENISYS Resource Manager - System Information")
        print("="*60)
        
        print(f"\n🖥️  System Resources:")
        print(f"   CPU Cores (Physical): {self.system_info['cpu_physical_cores']}")
        print(f"   CPU Cores (Logical):  {self.system_info['cpu_logical_cores']}")
        print(f"   Total RAM:            {self.system_info['total_ram_gb']:.1f} GB")
        print(f"   Available RAM:        {self.system_info['available_ram_gb']:.1f} GB")
        if self.system_info['max_cpu_freq_mhz']:
            print(f"   Max CPU Frequency:    {self.system_info['max_cpu_freq_mhz']:.0f} MHz")
        
        tier = self.config["system_tier"]
        limits = self.config["processing_limits"]
        
        print(f"\n⚙️  Configuration (Tier: {tier.upper()}):")
        print(f"   Max RAM Usage:        {limits['max_ram_gb']} GB ({limits['ram_usage_percent']*100:.0f}% of total)")
        print(f"   Max Workers:          {limits['max_workers']} ({limits['cpu_usage_percent']*100:.0f}% of logical cores)")
        print(f"   Chunk Size:           {limits['chunk_size']:,} rows")
        print(f"   Batch Size:           {limits['batch_size']} columns")
        
        data_config = self.config["data_processing"]
        print(f"\n📊 Data Processing:")
        print(f"   Polars Streaming:     {'Enabled' if data_config['polars_streaming'] else 'Disabled'}")
        print(f"   Pandas Chunk Size:    {data_config['pandas_chunksize']:,}")
        print(f"   GC Frequency:         Every {data_config['gc_frequency']} operations")
        
        ml_config = self.config["ml_training"]
        print(f"\n🤖 ML Configuration:")
        print(f"   LightGBM Threads:     {ml_config['lightgbm_num_threads']}")
        print(f"   VectorBT Workers:     {ml_config['vectorbt_num_workers']}")
        print(f"   Sklearn Jobs:         {ml_config['sklearn_n_jobs']}")
        
        usage_percent, status = self.check_memory_usage()
        status_emoji = {"OK": "✅", "HIGH": "⚠️", "WARNING": "🟡", "CRITICAL": "🔴"}
        print(f"\n💾 Current Memory Usage: {usage_percent:.1f}% {status_emoji.get(status, '❓')} ({status})")
        
        print("="*60)

def create_resource_aware_config(base_config_path: str, output_path: str = None) -> str:
    """Create a resource-aware version of an existing config file."""
    rm = ResourceManager()
    
    # Load base config
    with open(base_config_path, 'r') as f:
        base_config = json.load(f)
    
    # Add resource-aware parameters
    ml_config = rm.get_ml_config()
    processing_config = rm.get_processing_config()
    
    # Update model parameters with resource-aware settings
    if "strategy" in base_config and "model_params" in base_config["strategy"]:
        base_config["strategy"]["model_params"]["n_jobs"] = ml_config["lightgbm_num_threads"]
        base_config["strategy"]["model_params"]["num_threads"] = ml_config["lightgbm_num_threads"]
    
    # Add resource configuration section
    base_config["resource_config"] = {
        "max_ram_gb": processing_config["max_ram_gb"],
        "max_workers": processing_config["max_workers"],
        "chunk_size": processing_config["chunk_size"],
        "batch_size": processing_config["batch_size"],
        "enable_memory_monitoring": True
    }
    
    # Save resource-aware config
    if output_path is None:
        name_parts = Path(base_config_path).stem.split('_')
        output_path = f"{'_'.join(name_parts)}_resource_aware.json"
    
    with open(output_path, 'w') as f:
        json.dump(base_config, f, indent=2)
    
    print(f"Created resource-aware config: {output_path}")
    return output_path

if __name__ == "__main__":
    # Demo the resource manager
    rm = ResourceManager()
    rm.print_system_info()
    
    # Create resource-aware versions of existing configs
    for config_file in ["config.json", "config_v2.json"]:
        if Path(config_file).exists():
            create_resource_aware_config(config_file)
