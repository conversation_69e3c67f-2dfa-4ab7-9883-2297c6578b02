#!/usr/bin/env python
#
# GENISYS Project - Master Experiment Runner (V4 - Control Panel Edition)
#
# This script is the final, production-ready research engine.
# To run a new experiment, simply change the CONFIG_FILE variable
# in the SETTINGS section below and run the script from the terminal.
#

import json
import time
import warnings
from pathlib import Path

import numpy as np
import pandas as pd
import pandas_ta as ta
import vectorbt as vbt
import lightgbm as lgb
from numba import njit
from sqlalchemy import create_engine, text

# --- Suppress Warnings for Cleaner Output ---
warnings.simplefilter(action='ignore', category=FutureWarning)
warnings.simplefilter(action='ignore', category=pd.errors.PerformanceWarning)

# ==============================================================================
# === SCRIPT CONTROL PANEL ===
# ==============================================================================
SETTINGS = {
    # This is the ONLY line you need to change to run a new experiment.
    "CONFIG_FILE": "config_v2.json",
    
    # Set to True to do a quick "dry run" with only a small slice of data
    "DRY_RUN": False, 
    "DRY_RUN_BARS": 100000 # Number of bars to use for a dry run
}
# ==============================================================================

# === DATABASE HELPER FUNCTIONS === (No changes here)
def get_db_connection(db_config):
    try:
        conn_str = f"postgresql+psycopg2://{db_config['user']}:{db_config['password']}@{db_config['host']}/{db_config['dbname']}"
        engine = create_engine(conn_str)
        with engine.connect() as connection: pass
        return engine
    except Exception as e:
        print(f"!! FATAL: Could not connect to database. Error: {e}"); return None

def create_db_tables(engine):
    with engine.connect() as connection:
        connection.execute(text("CREATE TABLE IF NOT EXISTS experiments (experiment_id SERIAL PRIMARY KEY, run_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, strategy_name VARCHAR(255) NOT NULL, config_json JSONB, notes TEXT);"))
        connection.execute(text("CREATE TABLE IF NOT EXISTS results (result_id SERIAL PRIMARY KEY, experiment_id INTEGER REFERENCES experiments(experiment_id), parameters JSONB, total_return FLOAT, sharpe_ratio FLOAT, win_rate FLOAT, total_trades INTEGER, profit_factor FLOAT);"))
        connection.commit()

def log_experiment_to_db(engine, config):
    with engine.connect() as connection:
        result = connection.execute(text("INSERT INTO experiments (strategy_name, config_json, notes) VALUES (:name, :config, :notes) RETURNING experiment_id;"), {"name": config['experiment_name'], "config": json.dumps(config), "notes": config.get('notes', '')})
        experiment_id = result.scalar_one()
        connection.commit()
    return experiment_id

def log_result_to_db(engine, experiment_id, result_data):
    with engine.connect() as connection:
        connection.execute(text("INSERT INTO results (experiment_id, parameters, total_return, sharpe_ratio, win_rate, total_trades, profit_factor) VALUES (:exp_id, :params, :return, :sharpe, :win_rate, :trades, :pf);"), {"exp_id": experiment_id, "params": json.dumps(result_data['parameters']), "return": result_data['total_return'], "sharpe": result_data['sharpe_ratio'], "win_rate": result_data['win_rate'], "trades": result_data['total_trades'], "pf": result_data['profit_factor']})
        connection.commit()

# === LABELING HELPER FUNCTION === (No changes here)
@njit
def generate_labels_nb(price, highs, lows, atr, tp_m, sl_m, limit):
    n = len(price); labels = np.zeros(n, dtype=np.int8)
    for i in range(n - limit):
        if not np.isnan(atr[i]):
            tp = price[i] + (atr[i] * tp_m); sl = price[i] - (atr[i] * sl_m)
            for j in range(1, limit + 1):
                if lows[i + j] <= sl: labels[i] = -1; break
                if highs[i + j] >= tp: labels[i] = 1; break
    return labels

# === STRATEGY EXECUTION FUNCTION ===
def run_ml_confidence_strategy(config, df, db_engine, experiment_id):
    print("\n--- Executing 'ml_confidence' Strategy ---")
    cfg = config['strategy']; base_tf = cfg['base_timeframe']; feature_cols = cfg['feature_set']
    label_p = cfg['labeling_params']; model_p = cfg['model_params']; trade_p = cfg['trade_params']

    print("  -> Engineering relational features...")
    if 'dist_from_H1_EMA' in feature_cols:
        df['dist_from_H1_EMA'] = (df[f'{base_tf}_ASK_CLOSE'] - df['H1_ASK_EMA_50']) / df[f'{base_tf}_ASK_CLOSE']
    
    print(f"  -> Preparing data for {base_tf} timeframe...")
    close_price = df[f'{base_tf}_ASK_CLOSE']
    atr = ta.atr(high=df[f'{base_tf}_ASK_HIGH'], low=df[f'{base_tf}_ASK_LOW'], close=close_price, length=14)
    
    labels_array = generate_labels_nb(close_price.values, df[f'{base_tf}_ASK_HIGH'].values, df[f'{base_tf}_BID_LOW'].values, atr.values, label_p['tp_multiplier'], label_p['sl_multiplier'], label_p['time_limit'])
    df['label'] = pd.Series(labels_array, index=df.index, dtype='int8')

    print("  -> Preparing feature set and splitting data...")
    full_data = pd.concat([df[feature_cols], df['label']], axis=1).dropna()
    X, y = full_data[feature_cols], full_data['label']
    train_size = int(len(X) * 0.8)
    X_train, X_test, y_train, y_test = X[:train_size], X[train_size:], y[:train_size], y[train_size:]

    print("  -> Training LightGBM model...")
    lgb_clf = lgb.LGBMClassifier(objective='multiclass', n_jobs=-1, **model_p)
    lgb_clf.fit(X_train, y_train)

    print("  -> Making probability predictions...")
    y_pred_proba = lgb_clf.predict_proba(X_test)
    
    confidence_thresholds = trade_p.get('confidence_thresholds', [trade_p.get('confidence_threshold')])

    for thresh in confidence_thresholds:
        if thresh is None: continue
        print(f"    -> Backtesting with confidence threshold > {thresh:.2f}...")
        
        long_entries = y_pred_proba[:, lgb_clf.classes_ == 1].flatten() > thresh
        short_entries = y_pred_proba[:, lgb_clf.classes_ == -1].flatten() > thresh
        long_entries = pd.Series(long_entries, index=X_test.index)
        short_entries = pd.Series(short_entries, index=X_test.index)
        
        if long_entries.sum() == 0 and short_entries.sum() == 0:
            print("      -> No trades generated for this threshold. Skipping."); continue
            
        pf = vbt.Portfolio.from_signals(
            close=df[f'{base_tf}_ASK_CLOSE'].loc[X_test.index], open=df[f'{base_tf}_ASK_OPEN'].loc[X_test.index],
            high=df[f'{base_tf}_ASK_HIGH'].loc[X_test.index], low=df[f'{base_tf}_BID_LOW'].loc[X_test.index],
            entries=long_entries, short_entries=short_entries,
            sl_stop=(atr.loc[X_test.index] * label_p['sl_multiplier']), 
            tp_stop=(atr.loc[X_test.index] * label_p['tp_multiplier']),
            fees=0.0001, slippage=0.0001, freq=f"{base_tf[1:]}{base_tf[0].lower()}"
        )
        
        if pf.trades.count() > 5:
            stats = pf.stats()
            result_data = {
                'parameters': {'confidence_threshold': thresh},
                'total_return': float(stats.get('Total Return [%]', 0.0)), 'sharpe_ratio': float(stats.get('Sharpe Ratio', 0.0)),
                'win_rate': float(stats.get('Win Rate [%]', 0.0)), 'total_trades': int(stats.get('Total Trades', 0)),
                'profit_factor': float(stats.get('Profit Factor', 0.0))
            }
            log_result_to_db(db_engine, experiment_id, result_data)
            print(f"      -> Logged to DB: Sharpe = {result_data['sharpe_ratio']:.2f}, Trades = {result_data['total_trades']}")
        else:
            print(f"      -> Not enough trades ({pf.trades.count()}) to log result.")

# === MAIN SCRIPT EXECUTION ===
def main():
    """The main entry point of the script."""
    print("--- GENISYS: Master Research Engine Initialized ---")
    
    config_file_path = Path(SETTINGS["CONFIG_FILE"])
    try:
        with open(config_file_path, 'r') as f:
            config = json.load(f)
        print(f"Loaded experiment '{config['experiment_name']}' from {config_file_path}")
    except Exception as e:
        print(f"!! FATAL: Could not read or parse {config_file_path}. Error: {e}"); return

    db_engine = get_db_connection(config['database'])
    if db_engine is None: return
    create_db_tables(db_engine)
    experiment_id = log_experiment_to_db(db_engine, config)

    print(f"\nLoading data from {config['data']['source_file']}...")
    try:
        df = pd.read_parquet(config['data']['source_file'])
        df = df.set_index('Time (UTC)')
        # If DRY_RUN is enabled, slice the dataframe to make the run faster
        if SETTINGS["DRY_RUN"]:
            print(f"!!! DRY RUN ENABLED: Using only first {SETTINGS['DRY_RUN_BARS']} bars. !!!")
            df = df.head(SETTINGS["DRY_RUN_BARS"])
    except Exception as e:
        print(f"!! FATAL: Could not load source data file. Error: {e}"); return
        
    strategy_type = config['strategy'].get('type')
    if strategy_type == 'ml_confidence':
        run_ml_confidence_strategy(config, df, db_engine, experiment_id)
    else:
        print(f"!! FATAL: Unknown strategy type '{strategy_type}' in config file."); return
        
    print("\n--- GENISYS: Experiment Run Complete ---")


if __name__ == "__main__":
    main()
