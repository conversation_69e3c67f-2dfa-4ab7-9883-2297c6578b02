#!/usr/bin/env python
"""
GENISYS Project Setup Script

This script helps set up the GENISYS trading research platform.
Run this after cloning the repository to prepare your environment.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header(text):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"  {text}")
    print(f"{'='*60}")

def print_step(step_num, total_steps, description):
    """Print a formatted step."""
    print(f"\n[{step_num}/{total_steps}] {description}")

def run_command(command, description=""):
    """Run a shell command and handle errors."""
    try:
        print(f"  Running: {command}")
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        if result.stdout:
            print(f"  Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"  ERROR: {description} failed")
        print(f"  Command: {command}")
        print(f"  Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"ERROR: Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def create_virtual_environment():
    """Create and activate virtual environment."""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("  Virtual environment already exists")
        return True
    
    # Create virtual environment
    if not run_command(f"{sys.executable} -m venv .venv", "Virtual environment creation"):
        return False
    
    print("  ✓ Virtual environment created")
    return True

def get_activation_command():
    """Get the correct activation command for the platform."""
    if platform.system() == "Windows":
        return ".venv\\Scripts\\activate"
    else:
        return "source .venv/bin/activate"

def install_requirements():
    """Install Python requirements."""
    # Determine pip path
    if platform.system() == "Windows":
        pip_path = ".venv\\Scripts\\pip"
    else:
        pip_path = ".venv/bin/pip"
    
    # Upgrade pip first
    if not run_command(f"{pip_path} install --upgrade pip", "Pip upgrade"):
        return False
    
    # Install requirements
    if not run_command(f"{pip_path} install -r requirements.txt", "Requirements installation"):
        return False
    
    print("  ✓ Requirements installed")
    return True

def create_directories():
    """Create necessary directories."""
    directories = ["data", "logs", "models", "reports"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✓ Created directory: {directory}/")
    
    return True

def create_sample_config():
    """Create a sample configuration file for development."""
    sample_config = {
        "experiment_name": "SAMPLE_Development_Test",
        "notes": "Sample configuration for development and testing",
        "data": {
            "source_file": "data/GENISYS_EURUSD_MASTER.parquet"
        },
        "strategy": {
            "type": "ml_confidence",
            "base_timeframe": "M2",
            "feature_set": [
                "dist_from_H1_EMA", 
                "H1_ASK_EMA_50", 
                "D_ASK_RSI_14", 
                "S30_SPREAD_AVG"
            ],
            "labeling_params": {
                "tp_multiplier": 2.0,
                "sl_multiplier": 1.5,
                "time_limit": 30
            },
            "model_params": {
                "class_weight": "balanced",
                "random_state": 42
            },
            "trade_params": {
                "confidence_threshold": 0.65
            }
        },
        "database": {
            "host": "localhost",
            "dbname": "genisys_dev",
            "user": "postgres",
            "password": "your_password_here"
        }
    }
    
    import json
    config_path = Path("config_dev.json")
    if not config_path.exists():
        with open(config_path, 'w') as f:
            json.dump(sample_config, f, indent=2)
        print(f"  ✓ Created sample config: {config_path}")
    else:
        print(f"  Sample config already exists: {config_path}")
    
    return True

def print_next_steps():
    """Print instructions for next steps."""
    activation_cmd = get_activation_command()
    
    print_header("Setup Complete! Next Steps:")
    print(f"""
1. Activate the virtual environment:
   {activation_cmd}

2. Copy your data files to the data directory:
   cp /path/to/your/EURUSD_Ticks.csv data/

3. Update database configuration in config files:
   - config.json
   - config_v2.json  
   - config_dev.json (for development)

4. Set up PostgreSQL database and update credentials

5. Test the installation:
   python validate_dataset.py

6. Create your master dataset:
   python create_master_dataset.py

7. Run your first experiment:
   python run_experiment.py

For more information, see README.md
""")

def main():
    """Main setup function."""
    print_header("GENISYS Trading Platform Setup")
    
    # Check Python version
    print_step(1, 6, "Checking Python version")
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    print_step(2, 6, "Creating virtual environment")
    if not create_virtual_environment():
        sys.exit(1)
    
    # Install requirements
    print_step(3, 6, "Installing Python requirements")
    if not install_requirements():
        sys.exit(1)
    
    # Create directories
    print_step(4, 6, "Creating project directories")
    if not create_directories():
        sys.exit(1)
    
    # Create sample config
    print_step(5, 6, "Creating sample configuration")
    if not create_sample_config():
        sys.exit(1)
    
    # Print next steps
    print_step(6, 6, "Setup complete")
    print_next_steps()

if __name__ == "__main__":
    main()
