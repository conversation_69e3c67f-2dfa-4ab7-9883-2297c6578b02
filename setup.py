#!/usr/bin/env python
"""
GENISYS Project Setup Script

This script helps set up the GENISYS trading research platform.
Run this after cloning the repository to prepare your environment.
"""

import sys
import subprocess
import platform
from pathlib import Path

def print_header(text):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"  {text}")
    print(f"{'='*60}")

def print_step(step_num, total_steps, description):
    """Print a formatted step."""
    print(f"\n[{step_num}/{total_steps}] {description}")

def run_command(command, description="", silent=False):
    """Run a shell command and handle errors."""
    try:
        if not silent:
            print(f"  Running: {command}")
        result = subprocess.run(command, shell=True, check=True,
                              capture_output=True, text=True)
        if result.stdout and not silent:
            print(f"  Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        if not silent:
            print(f"  ERROR: {description} failed")
            print(f"  Command: {command}")
            print(f"  Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"ERROR: Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def detect_virtual_environment():
    """Detect the type and status of virtual environment."""
    venv_path = Path(".venv")

    if not venv_path.exists():
        return None, None, None

    # Check for different venv types
    if (venv_path / "pyvenv.cfg").exists():
        # Standard venv or uv venv
        if platform.system() == "Windows":
            python_path = venv_path / "Scripts" / "python.exe"
            pip_path = venv_path / "Scripts" / "pip.exe"
            activate_cmd = str(venv_path / "Scripts" / "activate.bat")
        else:
            python_path = venv_path / "bin" / "python"
            pip_path = venv_path / "bin" / "pip"
            activate_cmd = f"source {venv_path / 'bin' / 'activate'}"

        # Check if it's a uv environment
        is_uv = False
        try:
            with open(venv_path / "pyvenv.cfg") as f:
                content = f.read()
                if "uv" in content.lower():
                    is_uv = True
        except:
            pass

        return ("uv" if is_uv else "venv"), python_path, pip_path, activate_cmd

    return None, None, None, None

def create_virtual_environment():
    """Create virtual environment using available tools."""
    venv_type, python_path, pip_path, activate_cmd = detect_virtual_environment()

    if venv_type:
        print(f"  Virtual environment already exists (type: {venv_type})")
        return True, python_path, pip_path, activate_cmd

    print("  Creating new virtual environment...")

    # Try uv first (if available), then fall back to venv
    uv_available = run_command("uv --version", "UV version check", silent=True)

    if uv_available:
        print("  Using uv to create virtual environment...")
        if not run_command("uv venv .venv", "UV virtual environment creation"):
            return False, None, None, None
        venv_type = "uv"
    else:
        print("  Using standard venv...")
        if not run_command(f"{sys.executable} -m venv .venv", "Standard virtual environment creation"):
            return False, None, None, None
        venv_type = "venv"

    # Get paths for the newly created environment
    _, python_path, pip_path, activate_cmd = detect_virtual_environment()

    print(f"  ✓ Virtual environment created using {venv_type}")
    return True, python_path, pip_path, activate_cmd

def get_activation_command(activate_cmd=None):
    """Get the correct activation command for the platform."""
    if activate_cmd:
        return activate_cmd

    # Fallback to standard paths
    if platform.system() == "Windows":
        return ".venv\\Scripts\\activate"
    else:
        return "source .venv/bin/activate"

def install_requirements(python_path, pip_path):
    """Install Python requirements using detected paths."""
    # Check if we're already in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

    if in_venv:
        print("  Already in virtual environment, using current pip")
        pip_cmd = "pip"
    else:
        # Use the detected paths
        if not pip_path or not pip_path.exists():
            print("  ❌ Could not find pip in virtual environment")
            return False
        pip_cmd = str(pip_path)

    # Check if uv is available and prefer it for installations
    uv_available = run_command("uv --version", "UV availability check", silent=True)

    if uv_available:
        print("  Using uv for package installation...")
        # Use uv pip for faster installation
        if not run_command("uv pip install --upgrade pip", "Pip upgrade with uv"):
            print("  Falling back to standard pip upgrade...")
            if not run_command(f"{pip_cmd} install --upgrade pip", "Standard pip upgrade"):
                print("  ⚠️  Pip upgrade failed, continuing anyway...")

        if not run_command("uv pip install -r requirements.txt", "Requirements installation with uv"):
            print("  Falling back to standard pip...")
            if not run_command(f"{pip_cmd} install -r requirements.txt", "Standard requirements installation"):
                return False
    else:
        print("  Using standard pip...")
        # Upgrade pip first
        if not run_command(f"{pip_cmd} install --upgrade pip", "Pip upgrade"):
            print("  ⚠️  Pip upgrade failed, continuing anyway...")

        # Install requirements
        if not run_command(f"{pip_cmd} install -r requirements.txt", "Requirements installation"):
            return False

    print("  ✓ Requirements installed")
    return True

def create_directories():
    """Create necessary directories."""
    directories = ["data", "logs", "models", "reports"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✓ Created directory: {directory}/")
    
    return True

def create_sample_config():
    """Create a sample configuration file for development."""
    sample_config = {
        "experiment_name": "SAMPLE_Development_Test",
        "notes": "Sample configuration for development and testing",
        "data": {
            "source_file": "data/GENISYS_EURUSD_MASTER.parquet"
        },
        "strategy": {
            "type": "ml_confidence",
            "base_timeframe": "M2",
            "feature_set": [
                "dist_from_H1_EMA", 
                "H1_ASK_EMA_50", 
                "D_ASK_RSI_14", 
                "S30_SPREAD_AVG"
            ],
            "labeling_params": {
                "tp_multiplier": 2.0,
                "sl_multiplier": 1.5,
                "time_limit": 30
            },
            "model_params": {
                "class_weight": "balanced",
                "random_state": 42
            },
            "trade_params": {
                "confidence_threshold": 0.65
            }
        },
        "database": {
            "host": "localhost",
            "dbname": "genisys_dev",
            "user": "postgres",
            "password": "your_password_here"
        }
    }
    
    import json
    config_path = Path("config_dev.json")
    if not config_path.exists():
        with open(config_path, 'w') as f:
            json.dump(sample_config, f, indent=2)
        print(f"  ✓ Created sample config: {config_path}")
    else:
        print(f"  Sample config already exists: {config_path}")
    
    return True

def print_next_steps(activation_cmd=None):
    """Print instructions for next steps."""
    if not activation_cmd:
        activation_cmd = get_activation_command()

    print_header("Setup Complete! Next Steps:")
    print(f"""
1. Activate the virtual environment:
   {activation_cmd}

2. Copy your data files to the data directory:
   cp /path/to/your/EURUSD_Ticks.csv data/

3. Update database configuration in config files:
   - config.json
   - config_v2.json
   - config_dev.json (for development)

4. Set up PostgreSQL database and update credentials

5. Test the installation:
   python test_installation.py

6. Create your master dataset:
   python create_master_dataset.py

7. Run your first experiment:
   python run_experiment.py

For more information, see README.md or QUICKSTART.md
""")

def main():
    """Main setup function."""
    print_header("GENISYS Trading Platform Setup")

    # Check Python version
    print_step(1, 6, "Checking Python version")
    if not check_python_version():
        sys.exit(1)

    # Create/detect virtual environment
    print_step(2, 6, "Setting up virtual environment")
    success, python_path, pip_path, activate_cmd = create_virtual_environment()
    if not success:
        sys.exit(1)

    # Install requirements
    print_step(3, 6, "Installing Python requirements")
    if not install_requirements(python_path, pip_path):
        sys.exit(1)

    # Create directories
    print_step(4, 6, "Creating project directories")
    if not create_directories():
        sys.exit(1)

    # Create sample config
    print_step(5, 6, "Creating sample configuration")
    if not create_sample_config():
        sys.exit(1)

    # Print next steps
    print_step(6, 6, "Setup complete")
    print_next_steps(activate_cmd)

if __name__ == "__main__":
    main()
