#!/usr/bin/env python
"""
GENISYS Installation Test Script

This script tests that all dependencies are properly installed
and the environment is correctly configured.
"""

import sys
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("Testing Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current: {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def test_imports():
    """Test that all required packages can be imported."""
    print("\nTesting package imports...")
    
    required_packages = [
        ('pandas', 'pandas'),
        ('polars', 'polars'), 
        ('numpy', 'numpy'),
        ('pandas_ta', 'pandas_ta'),
        ('vectorbt', 'vectorbt'),
        ('lightgbm', 'lightgbm'),
        ('sqlalchemy', 'sqlalchemy'),
        ('psycopg2', 'psycopg2'),
        ('numba', 'numba'),
        ('plotly', 'plotly'),
        ('sklearn', 'scikit-learn'),
    ]
    
    failed_imports = []
    
    for module_name, package_name in required_packages:
        try:
            importlib.import_module(module_name)
            print(f"✅ {package_name}")
        except ImportError as e:
            print(f"❌ {package_name}: {e}")
            failed_imports.append(package_name)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True

def test_directory_structure():
    """Test that required directories exist."""
    print("\nTesting directory structure...")
    
    required_dirs = ['data']
    optional_dirs = ['logs', 'models', 'reports']
    
    all_good = True
    
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"✅ {directory}/")
        else:
            print(f"❌ {directory}/ (required)")
            all_good = False
    
    for directory in optional_dirs:
        if Path(directory).exists():
            print(f"✅ {directory}/")
        else:
            print(f"⚠️  {directory}/ (optional, will be created)")
    
    return all_good

def test_config_files():
    """Test that configuration files exist."""
    print("\nTesting configuration files...")
    
    config_files = [
        'config.json',
        'config_v2.json',
        'requirements.txt',
        'README.md'
    ]
    
    all_good = True
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file}")
            all_good = False
    
    return all_good

def test_data_files():
    """Test for data files (optional)."""
    print("\nTesting data files...")
    
    tick_data = Path("data/EURUSD_Ticks.csv")
    master_data = Path("data/GENISYS_EURUSD_MASTER.parquet")
    
    if tick_data.exists():
        print(f"✅ {tick_data}")
        print(f"   Size: {tick_data.stat().st_size / (1024**3):.2f} GB")
    else:
        print(f"⚠️  {tick_data} (copy your tick data here)")
    
    if master_data.exists():
        print(f"✅ {master_data}")
        print(f"   Size: {master_data.stat().st_size / (1024**3):.2f} GB")
    else:
        print(f"⚠️  {master_data} (will be created by create_master_dataset.py)")
    
    return True  # Data files are optional for installation test

def test_script_execution():
    """Test that main scripts can be imported without errors."""
    print("\nTesting script imports...")
    
    scripts = [
        'create_master_dataset',
        'run_experiment', 
        'validate_dataset'
    ]
    
    all_good = True
    
    for script in scripts:
        try:
            importlib.import_module(script)
            print(f"✅ {script}.py")
        except Exception as e:
            print(f"❌ {script}.py: {e}")
            all_good = False
    
    return all_good

def main():
    """Run all installation tests."""
    print("🧪 GENISYS Installation Test")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_imports,
        test_directory_structure,
        test_config_files,
        test_data_files,
        test_script_execution
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ Installation appears to be successful!")
        print("\nNext steps:")
        print("1. Copy your EURUSD_Ticks.csv to data/ directory")
        print("2. Update database credentials in config files")
        print("3. Run: python create_master_dataset.py")
        return True
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print("\n❌ Installation needs attention!")
        print("\nPlease fix the failed tests above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
