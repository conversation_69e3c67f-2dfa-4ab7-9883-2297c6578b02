#!/usr/bin/env python
#
# GENISYS Project - Master Dataset Validation Script (V2)
#

import polars as pl
from pathlib import Path
import time

# --- Configuration ---
DATA_DIR = Path("data")
MASTER_DATA_FILE = DATA_DIR / "GENISYS_EURUSD_MASTER.parquet"
VALIDATION_REPORT_FILE = "validation_report.txt"

def validate_dataset():
    start_time = time.time()
    print("--- GENISYS: Dataset Validation Initialized ---")
    
    report_lines = []
    
    # --- 1. Load Data ---
    print(f"\n[1/4] Loading master dataset: {MASTER_DATA_FILE}...")
    try:
        df = pl.read_parquet(MASTER_DATA_FILE)
        report_lines.append(f"--- Data Loading & Shape ---")
        report_lines.append(f"Successfully loaded the dataset.")
        report_lines.append(f"Shape: {df.shape} (rows, columns)\n")
    except Exception as e:
        print(f"FATAL: Could not load the dataset. Error: {e}"); return

    # --- 2. Check for Null/Missing Values ---
    print("[2/4] Checking for unexpected null values...")
    report_lines.append(f"--- Null Value Check ---")

    # Use the descriptive statistics that we'll generate anyway to check for nulls
    # This is more efficient than separate null checking for large datasets
    print("    -> Using descriptive statistics for null analysis...")
    report_lines.append(f"INFO: Null value analysis integrated with descriptive statistics below.")
    report_lines.append(f"Check the 'null_count' row in the statistics table.\n")

    # --- 3. Run Descriptive Statistics ---
    print("[3/4] Generating descriptive statistics for key features...")
    cols_to_describe = [
        "S5_ASK_CLOSE", "S5_TICK_VOLUME", "M5_ASK_RSI_14", 
        "M5_BID_BBU_20_2.0", "M5_ASK_ADX_14", "H1_ASK_EMA_50", 
        "D_ASK_CLOSE", "Time_HourOfDay", "M2_dist_from_H1_EMA"
    ]
    existing_cols_to_describe = [col for col in cols_to_describe if col in df.columns]
    
    if not existing_cols_to_describe:
        report_lines.append("!! WARNING: None of the key columns for description were found.")
    else:
        stats_df = df.select(existing_cols_to_describe).describe()
        report_lines.append(f"--- Descriptive Statistics Summary ---")
        report_lines.append("Check for logical inconsistencies (e.g., RSI > 100, negative prices).\n")
        # Use Polars' built-in table formatting for a clean report
        report_lines.append(stats_df.to_pandas().to_string())

    # --- 4. Save the Report ---
    print(f"[4/4] Saving validation report to: {VALIDATION_REPORT_FILE}...")
    try:
        with open(VALIDATION_REPORT_FILE, 'w') as f:
            f.write("\n".join(report_lines))
        print("SUCCESS: Report saved.")
    except Exception as e:
        print(f"FATAL: Could not save the report. Error: {e}")
        
    end_time = time.time()
    print("\n--- GENISYS: Dataset Validation Complete ---")
    print(f"Total time taken: {end_time - start_time:.2f} seconds.")

if __name__ == "__main__":
    validate_dataset()
