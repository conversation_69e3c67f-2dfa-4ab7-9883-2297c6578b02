#!/usr/bin/env python
"""
GENISYS-ANALYTIC Standalone Verification Script

Verifies that GENISYS-ANALYTIC is clean and ready to be separated from GENISYS-WEB.
"""

from pathlib import Path
import sys

def check_for_web_files():
    """Check for any remaining GENISYS-WEB related files."""
    print("🔍 Checking for GENISYS-WEB related files...")
    
    web_related_files = [
        "create_genisys_web.py",
        "deploy_genisys_system.py", 
        "GENISYS_WEB_ARCHITECTURE.md",
        "DEPLOYMENT_GUIDE.md",
        "SEPARATION_GUIDE.md"
    ]
    
    found_files = []
    for file in web_related_files:
        if Path(file).exists():
            found_files.append(file)
    
    if found_files:
        print(f"⚠️  Found web-related files that should be removed:")
        for file in found_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ No web-related files found")
        return True

def check_analytic_files():
    """Check that all required GENISYS-ANALYTIC files are present."""
    print("\n🔍 Checking for required GENISYS-ANALYTIC files...")
    
    required_files = [
        "README.md",
        "QUICKSTART.md", 
        "RESOURCE_MANAGEMENT.md",
        "run_experiment.py",
        "worker_daemon.py",
        "experiment_runner.py",
        "resource_manager.py",
        "manage_resources.py",
        "create_master_dataset.py",
        "validate_dataset.py",
        "test_installation.py",
        "setup.py",
        "requirements.txt",
        "config.json",
        "config_v2.json"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ All required files present")
        return True

def check_directories():
    """Check that required directories exist."""
    print("\n🔍 Checking directory structure...")
    
    required_dirs = ["data"]
    optional_dirs = ["models", "logs", ".venv"]
    
    missing_required = []
    for directory in required_dirs:
        if not Path(directory).exists():
            missing_required.append(directory)
    
    if missing_required:
        print(f"❌ Missing required directories:")
        for directory in missing_required:
            print(f"   - {directory}/")
        return False
    
    print("✅ Required directories present")
    
    # Check optional directories
    for directory in optional_dirs:
        if Path(directory).exists():
            print(f"✅ Optional directory present: {directory}/")
        else:
            print(f"ℹ️  Optional directory missing: {directory}/ (will be created as needed)")
    
    return True

def check_imports():
    """Test that core modules can be imported."""
    print("\n🔍 Testing core module imports...")
    
    modules_to_test = [
        ("resource_manager", "ResourceManager"),
        ("run_experiment", "main"),
        ("worker_daemon", "WorkerDaemon"),
        ("experiment_runner", "ExperimentRunner")
    ]
    
    failed_imports = []
    
    for module_name, class_or_function in modules_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, class_or_function):
                print(f"✅ {module_name}.{class_or_function}")
            else:
                print(f"⚠️  {module_name} imported but {class_or_function} not found")
                failed_imports.append(f"{module_name}.{class_or_function}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            failed_imports.append(module_name)
    
    return len(failed_imports) == 0

def check_documentation():
    """Check that documentation is up to date."""
    print("\n🔍 Checking documentation...")
    
    readme_path = Path("README.md")
    if not readme_path.exists():
        print("❌ README.md not found")
        return False
    
    readme_content = readme_path.read_text()
    
    # Check for updated title
    if "GENISYS-ANALYTIC" in readme_content:
        print("✅ README.md has correct title")
    else:
        print("⚠️  README.md may need title update")
    
    # Check for distributed mode documentation
    if "worker_daemon.py" in readme_content:
        print("✅ README.md documents distributed mode")
    else:
        print("⚠️  README.md may need distributed mode documentation")
    
    # Check for project structure
    if "Project Structure" in readme_content:
        print("✅ README.md has project structure")
    else:
        print("⚠️  README.md missing project structure")
    
    return True

def check_genisys_web_directory():
    """Check if GENISYS-WEB directory exists and warn about separation."""
    print("\n🔍 Checking for GENISYS-WEB directory...")
    
    web_dir = Path("GENISYS-WEB")
    if web_dir.exists():
        print("📁 GENISYS-WEB directory found")
        print("💡 Remember to move this directory out of GENISYS-ANALYTIC:")
        print("   cd .. && mv GENISYS/GENISYS-WEB ./")
        return False
    else:
        print("✅ GENISYS-WEB directory not present (good for standalone)")
        return True

def main():
    """Run all verification checks."""
    print("🔍 GENISYS-ANALYTIC Standalone Verification")
    print("=" * 50)
    
    checks = [
        ("Web Files Cleanup", check_for_web_files),
        ("Required Files", check_analytic_files),
        ("Directory Structure", check_directories),
        ("Module Imports", check_imports),
        ("Documentation", check_documentation),
        ("GENISYS-WEB Separation", check_genisys_web_directory)
    ]
    
    results = []
    for check_name, check_function in checks:
        print(f"\n📋 {check_name}")
        print("-" * 30)
        try:
            result = check_function()
            results.append(result)
        except Exception as e:
            print(f"❌ Check failed with error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("📊 Verification Summary")
    print("=" * 50)
    
    if passed == total:
        print(f"🎉 All checks passed! ({passed}/{total})")
        print("\n✅ GENISYS-ANALYTIC is ready for standalone operation!")
        print("\n📋 Next steps:")
        print("1. Move GENISYS-WEB out: cd .. && mv GENISYS/GENISYS-WEB ./")
        print("2. Rename project: mv GENISYS GENISYS-ANALYTIC")
        print("3. Test standalone: python test_installation.py")
        print("4. Test worker mode: python worker_daemon.py --help")
        return True
    else:
        print(f"⚠️  {passed}/{total} checks passed")
        print("\n❌ Issues need to be resolved before separation!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
