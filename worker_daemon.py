#!/usr/bin/env python
"""
GENISYS-ANALYTIC Worker Daemon

Connects to GENISYS-WEB server and executes trading strategy experiments.
Automatically registers worker capabilities and polls for experiment jobs.
"""

import asyncio
import argparse
import json
import socket
import time
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import httpx
import psutil

from resource_manager import ResourceManager
from experiment_runner import ExperimentRunner

class WorkerDaemon:
    """Worker daemon that connects to GENISYS-WEB server and executes experiments."""
    
    def __init__(self, server_url: str, worker_name: Optional[str] = None):
        self.server_url = server_url.rstrip('/')
        self.worker_name = worker_name or socket.gethostname()
        self.worker_id = None
        self.is_running = False
        
        # Initialize resource manager and experiment runner
        self.resource_manager = ResourceManager()
        self.experiment_runner = ExperimentRunner(self.resource_manager)
        
        # HTTP client for API communication
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # Current experiment tracking
        self.current_experiment = None
        self.experiment_start_time = None
        
    async def start(self):
        """Start the worker daemon."""
        print(f"🚀 Starting GENISYS-ANALYTIC Worker: {self.worker_name}")
        print(f"🌐 Connecting to server: {self.server_url}")
        
        try:
            # Register with the server
            await self.register_worker()
            
            # Start main worker loop
            self.is_running = True
            await self.worker_loop()
            
        except KeyboardInterrupt:
            print("\n⏹️  Shutdown requested by user")
        except Exception as e:
            print(f"❌ Worker error: {e}")
        finally:
            await self.shutdown()
    
    async def register_worker(self):
        """Register this worker with the GENISYS-WEB server."""
        system_info = self.resource_manager.system_info
        processing_config = self.resource_manager.get_processing_config()
        
        # Get local IP address
        try:
            # Connect to a remote address to determine local IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
        except:
            local_ip = "127.0.0.1"
        
        registration_data = {
            "name": self.worker_name,
            "hostname": socket.gethostname(),
            "ip_address": local_ip,
            "resource_config": {
                "cpu_physical_cores": system_info["cpu_physical_cores"],
                "cpu_logical_cores": system_info["cpu_logical_cores"],
                "total_ram_gb": system_info["total_ram_gb"],
                "max_ram_gb": processing_config["max_ram_gb"],
                "max_workers": processing_config["max_workers"],
                "chunk_size": processing_config["chunk_size"],
                "batch_size": processing_config["batch_size"]
            },
            "capabilities": {
                "supported_timeframes": ["S5", "S10", "S15", "S30", "M1", "M2", "M5", "M10", "M15", "M30", "H1", "H4", "D"],
                "supported_strategies": ["ml_confidence"],
                "max_concurrent_experiments": 1,  # Conservative for now
                "model_storage_enabled": True
            }
        }
        
        try:
            response = await self.client.post(
                f"{self.server_url}/api/workers/register",
                json=registration_data
            )
            response.raise_for_status()
            
            result = response.json()
            self.worker_id = result["worker_id"]
            
            print(f"✅ Registered as worker: {self.worker_id}")
            print(f"📊 Resources: {processing_config['max_ram_gb']}GB RAM, {processing_config['max_workers']} workers")
            
        except httpx.RequestError as e:
            print(f"❌ Failed to connect to server: {e}")
            raise
        except httpx.HTTPStatusError as e:
            print(f"❌ Registration failed: {e.response.status_code} - {e.response.text}")
            raise
    
    async def worker_loop(self):
        """Main worker loop - poll for jobs and send heartbeats."""
        heartbeat_interval = 30  # seconds
        job_poll_interval = 10   # seconds
        last_heartbeat = 0
        
        print(f"🔄 Worker loop started (polling every {job_poll_interval}s)")
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Send heartbeat
                if current_time - last_heartbeat >= heartbeat_interval:
                    await self.send_heartbeat()
                    last_heartbeat = current_time
                
                # Poll for new jobs if not currently running an experiment
                if self.current_experiment is None:
                    await self.poll_for_jobs()
                else:
                    # Check if current experiment is still running
                    if not self.experiment_runner.is_running():
                        await self.handle_experiment_completion()
                
                # Wait before next iteration
                await asyncio.sleep(job_poll_interval)
                
            except Exception as e:
                print(f"⚠️  Error in worker loop: {e}")
                await asyncio.sleep(job_poll_interval)
    
    async def send_heartbeat(self):
        """Send heartbeat to server with current status."""
        if not self.worker_id:
            return
        
        # Get current resource usage
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        heartbeat_data = {
            "status": "busy" if self.current_experiment else "idle",
            "current_experiment_id": self.current_experiment,
            "resource_usage": {
                "ram_usage_percent": memory.percent,
                "ram_usage_gb": memory.used / (1024**3),
                "cpu_usage_percent": cpu_percent
            },
            "uptime_seconds": int(time.time() - self.experiment_start_time) if self.experiment_start_time else 0
        }
        
        try:
            response = await self.client.post(
                f"{self.server_url}/api/workers/{self.worker_id}/heartbeat",
                json=heartbeat_data
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"⚠️  Heartbeat failed: {e}")
    
    async def poll_for_jobs(self):
        """Poll server for new experiment jobs."""
        if not self.worker_id:
            return
        
        try:
            response = await self.client.get(
                f"{self.server_url}/api/workers/{self.worker_id}/jobs"
            )
            response.raise_for_status()
            
            jobs = response.json()
            
            if jobs and len(jobs) > 0:
                # Take the first job
                job = jobs[0]
                await self.start_experiment(job)
                
        except httpx.HTTPStatusError as e:
            if e.response.status_code != 404:  # 404 means no jobs available
                print(f"⚠️  Error polling for jobs: {e}")
        except Exception as e:
            print(f"⚠️  Error polling for jobs: {e}")
    
    async def start_experiment(self, job: Dict[str, Any]):
        """Start executing an experiment."""
        experiment_id = job["experiment_id"]
        config = job["config"]
        
        print(f"🧪 Starting experiment: {experiment_id}")
        print(f"📋 Strategy: {config.get('experiment_name', 'Unknown')}")
        
        self.current_experiment = experiment_id
        self.experiment_start_time = time.time()
        
        # Notify server that experiment has started
        await self.report_experiment_status(experiment_id, "running", 0)
        
        # Start experiment in background
        asyncio.create_task(self.run_experiment(experiment_id, config))
    
    async def run_experiment(self, experiment_id: str, config: Dict[str, Any]):
        """Run the experiment and report results."""
        try:
            # Execute the experiment
            results = await self.experiment_runner.run_experiment(
                config, 
                progress_callback=lambda progress: asyncio.create_task(
                    self.report_experiment_progress(experiment_id, progress)
                )
            )
            
            # Report successful completion
            await self.report_experiment_results(experiment_id, results)
            print(f"✅ Experiment {experiment_id} completed successfully")
            
        except Exception as e:
            print(f"❌ Experiment {experiment_id} failed: {e}")
            await self.report_experiment_status(experiment_id, "failed", 100, str(e))
        
        finally:
            self.current_experiment = None
            self.experiment_start_time = None
    
    async def report_experiment_status(self, experiment_id: str, status: str, progress: float, error: str = None):
        """Report experiment status to server."""
        status_data = {
            "status": status,
            "progress_percent": progress,
            "worker_id": self.worker_id
        }
        
        if error:
            status_data["error_message"] = error
        
        try:
            response = await self.client.post(
                f"{self.server_url}/api/experiments/{experiment_id}/status",
                json=status_data
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"⚠️  Failed to report status: {e}")
    
    async def report_experiment_progress(self, experiment_id: str, progress_data: Dict[str, Any]):
        """Report experiment progress to server."""
        try:
            response = await self.client.post(
                f"{self.server_url}/api/experiments/{experiment_id}/progress",
                json=progress_data
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"⚠️  Failed to report progress: {e}")
    
    async def report_experiment_results(self, experiment_id: str, results: Dict[str, Any]):
        """Report experiment results to server."""
        try:
            response = await self.client.post(
                f"{self.server_url}/api/experiments/{experiment_id}/results",
                json=results
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"⚠️  Failed to report results: {e}")
    
    async def handle_experiment_completion(self):
        """Handle completion of current experiment."""
        if self.current_experiment:
            print(f"🏁 Experiment {self.current_experiment} completed")
            self.current_experiment = None
            self.experiment_start_time = None
    
    async def shutdown(self):
        """Gracefully shutdown the worker."""
        print("🛑 Shutting down worker...")
        
        self.is_running = False
        
        # Notify server of shutdown
        if self.worker_id:
            try:
                await self.client.post(
                    f"{self.server_url}/api/workers/{self.worker_id}/shutdown"
                )
            except:
                pass  # Server might be down
        
        # Close HTTP client
        await self.client.aclose()
        
        print("✅ Worker shutdown complete")

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="GENISYS-ANALYTIC Worker Daemon")
    parser.add_argument("--server-url", required=True, help="GENISYS-WEB server URL")
    parser.add_argument("--worker-name", help="Custom worker name (default: hostname)")
    parser.add_argument("--config", help="Custom resource config file")
    
    args = parser.parse_args()
    
    # Initialize worker daemon
    worker = WorkerDaemon(args.server_url, args.worker_name)
    
    # Start the worker
    await worker.start()

if __name__ == "__main__":
    asyncio.run(main())
